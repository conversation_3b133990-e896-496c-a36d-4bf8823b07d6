<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_504EgGBSEfCWTN1FKOuw5w" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_504EgWBSEfCWTN1FKOuw5w" bindingContexts="_504EimBSEfCWTN1FKOuw5w">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList/>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_504EgWBSEfCWTN1FKOuw5w" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_6HllImBSEfCWTN1FKOuw5w" label="%trimmedwindow.label.eclipseSDK" x="606" y="84" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1752456967443"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_6HllImBSEfCWTN1FKOuw5w" selectedElement="_6HmMMGBSEfCWTN1FKOuw5w" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_6HmMMGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_6R1tUGBSEfCWTN1FKOuw5w">
        <children xsi:type="advanced:Perspective" xmi:id="_6R1tUGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jst.j2ee.J2EEPerspective" selectedElement="_6R1tUWBSEfCWTN1FKOuw5w" label="J2EE" iconURI="platform:/plugin/org.eclipse.jst.j2ee.ui/icons/full/cview16/j2ee_perspective.gif">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.doc.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.jst.j2ee.J2eeMainActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.wst.server.ui.ServersView</tags>
          <tags>persp.viewSC:org.eclipse.datatools.connectivity.DataSourceExplorerNavigator</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.BookmarkView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.wst.common.snippets.internal.ui.SnippetsView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.AllMarkersView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.jdt.ui.PackagesView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.showIn:org.eclipse.jdt.ui.PackagesView</tags>
          <tags>persp.actionSet:org.eclipse.wst.ws.explorer.explorer</tags>
          <tags>persp.newWizSC:org.eclipse.m2e.core.wizards.Maven2ProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.wst.css.ui.internal.wizard.NewCSSWizard</tags>
          <tags>persp.newWizSC:org.eclipse.wst.jsdt.ui.NewJSWizard</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaPerspective</tags>
          <tags>persp.perspSC:org.eclipse.ui.resourcePerspective</tags>
          <tags>persp.perspSC:org.eclipse.wst.web.ui.webDevPerspective</tags>
          <tags>persp.newWizSC:org.eclipse.jst.j2ee.ui.project.facet.EarProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.servlet.ui.project.facet.WebProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ejb.ui.project.facet.EjbProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.j2ee.jca.ui.internal.wizard.ConnectorProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.j2ee.ui.project.facet.appclient.AppClientProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.wst.web.ui.internal.wizards.SimpleWebProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jpt.ui.wizard.newJpaProject</tags>
          <tags>persp.newWizSC:org.eclipse.jst.servlet.ui.internal.wizard.AddServletWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ejb.ui.internal.wizard.AddSessionBeanWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ejb.ui.internal.wizard.AddMessageDrivenBeanWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jpt.ui.wizard.newEntity</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ws.creation.ui.wizard.serverwizard</tags>
          <tags>persp.newWizSC:org.eclipse.wst.html.ui.internal.wizard.NewHTMLWizard</tags>
          <tags>persp.newWizSC:org.eclipse.wst.xml.ui.internal.wizards.NewXMLWizard</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.file</tags>
          <tags>persp.actionSet:org.eclipse.wst.server.ui.internal.webbrowser.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.actionSet:org.eclipse.eclemma.ui.CoverageActionSet</tags>
          <tags>persp.showIn:org.eclipse.eclemma.ui.CoverageView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.newWizSC:org.eclipse.jst.jsp.ui.internal.wizard.NewJSPWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jpt.jpa.ui.wizard.newJpaProject</tags>
          <tags>persp.perspSC:org.eclipse.jpt.ui.jpaPerspective</tags>
          <tags>persp.editorOnboardingText:Open a file or drop files here to open them.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$Ctrl+3</tags>
          <tags>persp.editorOnboardingCommand:Show Key Assist$$$Ctrl+Shift+L</tags>
          <tags>persp.editorOnboardingCommand:New$$$Ctrl+N</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_6R1tUWBSEfCWTN1FKOuw5w" selectedElement="_6R1tUmBSEfCWTN1FKOuw5w" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_6R1tUmBSEfCWTN1FKOuw5w" elementId="topLeft" containerData="2000" selectedElement="_6R1tU2BSEfCWTN1FKOuw5w">
              <tags>active</tags>
              <children xsi:type="advanced:Placeholder" xmi:id="_6R1tU2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_6M7_MGBSEfCWTN1FKOuw5w" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:&#x5e38;&#x89c4;</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_6R1tVGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.PackagesView" toBeRendered="false" ref="_6Rr8UGBSEfCWTN1FKOuw5w" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:Java &#x6d4f;&#x89c8;</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_6R1tVWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigator.ProjectExplorer" toBeRendered="false" ref="_6M7_MGBSEfCWTN1FKOuw5w" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:&#x5e38;&#x89c4;</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_6R1tVmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_6Rr8UWBSEfCWTN1FKOuw5w" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:Java</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_6R1tV2BSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.DebugView" toBeRendered="false" ref="_6Rr8UmBSEfCWTN1FKOuw5w" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:&#x8c03;&#x8bd5;</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_6R1tWGBSEfCWTN1FKOuw5w" elementId="org.eclipse.search.ui.views.SearchView" toBeRendered="false" ref="_6RsjYGBSEfCWTN1FKOuw5w" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:&#x5e38;&#x89c4;</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_6R1tWWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesView" toBeRendered="false" ref="_6RsjYWBSEfCWTN1FKOuw5w" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:Git</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_6R1tWmBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.VariableView" toBeRendered="false" ref="_6RvmsWBSEfCWTN1FKOuw5w" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:&#x8c03;&#x8bd5;</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_6R1tW2BSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.ExpressionView" toBeRendered="false" ref="_6RvmsmBSEfCWTN1FKOuw5w" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:&#x8c03;&#x8bd5;</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_6R1tXGBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.BreakpointView" toBeRendered="false" ref="_6Rvms2BSEfCWTN1FKOuw5w" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:&#x8c03;&#x8bd5;</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_6R1tXWBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.MemoryView" toBeRendered="false" ref="_6RvmtGBSEfCWTN1FKOuw5w" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:&#x8c03;&#x8bd5;</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_6R1tXmBSEfCWTN1FKOuw5w" containerData="8000">
              <children xsi:type="basic:PartSashContainer" xmi:id="_6R1tX2BSEfCWTN1FKOuw5w" containerData="8000" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_6R1tYGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.editorss" containerData="8000" ref="_6MyOMGBSEfCWTN1FKOuw5w"/>
                <children xsi:type="basic:PartStack" xmi:id="_6R1tYWBSEfCWTN1FKOuw5w" elementId="topRight" containerData="2000" selectedElement="_6R1tYmBSEfCWTN1FKOuw5w">
                  <children xsi:type="advanced:Placeholder" xmi:id="_6R1tYmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.ContentOutline" ref="_6Ru_pmBSEfCWTN1FKOuw5w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:&#x5e38;&#x89c4;</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_6R1tY2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_6RvmsGBSEfCWTN1FKOuw5w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:&#x5e38;&#x89c4;</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_6R1tZGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_6RvmsGBSEfCWTN1FKOuw5w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:&#x5e38;&#x89c4;</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_6R1tZWBSEfCWTN1FKOuw5w" elementId="bottomRight" containerData="2000" selectedElement="_6R1tZmBSEfCWTN1FKOuw5w">
                <children xsi:type="advanced:Placeholder" xmi:id="_6R1tZmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.ProblemView" ref="_6RsjYmBSEfCWTN1FKOuw5w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:&#x5e38;&#x89c4;</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_6R1tZ2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.server.ui.ServersView" ref="_6RtKcGBSEfCWTN1FKOuw5w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:&#x670d;&#x52a1;&#x5668;</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_6R1taGBSEfCWTN1FKOuw5w" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" ref="_6RtKcWBSEfCWTN1FKOuw5w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:&#x7ec8;&#x7aef;</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_6R1taWBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.connectivity.DataSourceExplorerNavigator" ref="_6RtxgGBSEfCWTN1FKOuw5w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:&#x6570;&#x636e;&#x7ba1;&#x7406;</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_6R1tamBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.PropertySheet" ref="_6RuYkGBSEfCWTN1FKOuw5w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:&#x5e38;&#x89c4;</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_6R1ta2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" toBeRendered="false" ref="_6RuYkWBSEfCWTN1FKOuw5w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:&#x5e38;&#x89c4;</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_6R1tbGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.AllMarkersView" toBeRendered="false" ref="_6RuYkmBSEfCWTN1FKOuw5w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:&#x5e38;&#x89c4;</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_6R1tbWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_6RuYk2BSEfCWTN1FKOuw5w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:&#x5e38;&#x89c4;</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_6R1tbmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.TaskList" toBeRendered="false" ref="_6Ru_oGBSEfCWTN1FKOuw5w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:&#x5e38;&#x89c4;</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_6R1tb2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.console.ConsoleView" toBeRendered="false" ref="_6Ru_oWBSEfCWTN1FKOuw5w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:&#x5e38;&#x89c4;</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_6R1tcGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_6Ru_omBSEfCWTN1FKOuw5w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:&#x5e38;&#x89c4;</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_6R1tcWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.JavadocView" toBeRendered="false" ref="_6Ru_o2BSEfCWTN1FKOuw5w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_6R1tcmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.MembersView" toBeRendered="false" ref="_6Ru_pGBSEfCWTN1FKOuw5w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java &#x6d4f;&#x89c8;</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_6R1tc2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.SourceView" toBeRendered="false" ref="_6Ru_pWBSEfCWTN1FKOuw5w" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Java</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_6HmMMWBSEfCWTN1FKOuw5w" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_6HmMMmBSEfCWTN1FKOuw5w" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_6HkXAGBSEfCWTN1FKOuw5w" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:&#x5e2e;&#x52a9;</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_6HmMM2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_6HllIGBSEfCWTN1FKOuw5w" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:&#x5e38;&#x89c4;</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_6HmMNGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_6HllIWBSEfCWTN1FKOuw5w" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:&#x5e2e;&#x52a9;</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_6HkXAGBSEfCWTN1FKOuw5w" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x5e2e;&#x52a9;" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:&#x5e2e;&#x52a9;</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6HllIGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view>&#xD;&#xA;&lt;presentation currentPage=&quot;qroot&quot; restore=&quot;true&quot;/>&#xD;&#xA;&lt;standbyPart/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:&#x5e38;&#x89c4;</tags>
      <tags>activeOnClose</tags>
      <menus xmi:id="_6hbBcGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_6hbogGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6HllIWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x5907;&#x5fd8;&#x5355;" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:&#x5e2e;&#x52a9;</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_6MyOMGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.editorss">
      <children xsi:type="basic:PartStack" xmi:id="_6MyOMWBSEfCWTN1FKOuw5w" elementId="org.eclipse.e4.primaryDataStack">
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>EditorStack</tags>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6M7_MGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; currentWorkingSetName=&quot;Aggregate for window 1752456967443&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;>&#xD;&#xA;&lt;lastRecentlyUsedFilters/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:&#x5e38;&#x89c4;</tags>
      <tags>active</tags>
      <tags>activeOnClose</tags>
      <menus xmi:id="_6WMo4GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_6WMo4WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigator.ProjectExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6Rr8UGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.PackagesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x5305;" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.PackagesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java &#x6d4f;&#x89c8;</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6Rr8UWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.TypeHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x7c7b;&#x578b;&#x5c42;&#x6b21;&#x7ed3;&#x6784;" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6Rr8UmBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.DebugView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x8c03;&#x8bd5;" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:&#x8c03;&#x8bd5;</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6RsjYGBSEfCWTN1FKOuw5w" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x641c;&#x7d22;" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
      <tags>View</tags>
      <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6RsjYWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Git &#x5b58;&#x50a8;&#x5e93;" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Git</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6RsjYmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;105&quot; org.eclipse.ui.ide.markerType=&quot;105&quot; org.eclipse.ui.ide.pathField=&quot;140&quot; org.eclipse.ui.ide.resourceField=&quot;105&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;350&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:&#x5e38;&#x89c4;</tags>
      <menus xmi:id="_6bnUYGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_6bnUYWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6RtKcGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.server.ui.ServersView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x670d;&#x52a1;&#x5668;" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.server.ui.internal.cnf.ServersView2"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.server.ui"/>
      <tags>View</tags>
      <tags>categoryTag:&#x670d;&#x52a1;&#x5668;</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6RtKcWBSEfCWTN1FKOuw5w" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x7ec8;&#x7aef;" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
      <tags>View</tags>
      <tags>categoryTag:&#x7ec8;&#x7aef;</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6RtxgGBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.connectivity.DataSourceExplorerNavigator" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x6570;&#x636e;&#x6e90;&#x8d44;&#x6e90;&#x7ba1;&#x7406;&#x5668;" iconURI="platform:/plugin/org.eclipse.datatools.connectivity.ui.dse/icons/full/cview16/enterprise_explorer.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.datatools.connectivity.ui.dse.views.DataSourceExplorerView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.datatools.connectivity.ui.dse"/>
      <tags>View</tags>
      <tags>categoryTag:&#x6570;&#x636e;&#x7ba1;&#x7406;</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6RuYkGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x5c5e;&#x6027;" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <tags>View</tags>
      <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6RuYkWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x7247;&#x6bb5;" iconURI="platform:/plugin/org.eclipse.wst.common.snippets/icons/snippets_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.common.snippets.internal.ui.SnippetsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.common.snippets"/>
      <tags>View</tags>
      <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6RuYkmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.AllMarkersView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x6807;&#x8bb0;" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6RuYk2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x4e66;&#x7b7e;" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6Ru_oGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x4efb;&#x52a1;" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6Ru_oWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x63a7;&#x5236;&#x53f0;" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <tags>View</tags>
      <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6Ru_omBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x8fdb;&#x5ea6;" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6Ru_o2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.JavadocView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6Ru_pGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.MembersView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x6210;&#x5458;" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.MembersView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java &#x6d4f;&#x89c8;</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6Ru_pWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.SourceView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x58f0;&#x660e;" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6Ru_pmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:&#x5e38;&#x89c4;</tags>
      <menus xmi:id="_6anO0GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_6an14GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6RvmsGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x5c0f;&#x5730;&#x56fe;" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6RvmsWBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.VariableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x53d8;&#x91cf;" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:&#x8c03;&#x8bd5;</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6RvmsmBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.ExpressionView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x8868;&#x8fbe;&#x5f0f;" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:&#x8c03;&#x8bd5;</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6Rvms2BSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.BreakpointView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x65ad;&#x70b9;" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:&#x8c03;&#x8bd5;</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_6RvmtGBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.MemoryView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x5185;&#x5b58;" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:&#x8c03;&#x8bd5;</tags>
    </sharedElements>
    <trimBars xmi:id="_504EgmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.platform">
      <children xsi:type="menu:ToolBar" xmi:id="_6Jb_QGBSEfCWTN1FKOuw5w" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_6Jb_QWBSEfCWTN1FKOuw5w" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_6JdNYGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_RvL40GBkEfCpeLIca-Fe2Q" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_51gWoGBSEfCWTN1FKOuw5w"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_6JdNYWBSEfCWTN1FKOuw5w" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_6JdNYmBSEfCWTN1FKOuw5w" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_6JdNY2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_RvNuAWBkEfCpeLIca-Fe2Q" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" enabled="false" command="_51a3OWBSEfCWTN1FKOuw5w"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_RvOVEGBkEfCpeLIca-Fe2Q" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="_51csbGBSEfCWTN1FKOuw5w"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_6JdNZGBSEfCWTN1FKOuw5w" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_6JdNZWBSEfCWTN1FKOuw5w" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_6TaaoGBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_6So-kGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jst.j2ee.J2eeMainActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_6TFDcGBSEfCWTN1FKOuw5w" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_6S1y4GBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.server.ui.internal.webbrowser.actionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_6TO0cGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.ws.explorer.explorer">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_6JdNZmBSEfCWTN1FKOuw5w" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_6JdNZ2BSEfCWTN1FKOuw5w" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_6Jd0cGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_RvO8ImBkEfCpeLIca-Fe2Q" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" enabled="false" type="Check" command="_51fvl2BSEfCWTN1FKOuw5w"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_6Jd0cWBSEfCWTN1FKOuw5w" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_6Jd0cmBSEfCWTN1FKOuw5w" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_6Jd0c2BSEfCWTN1FKOuw5w" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_6Jd0dGBSEfCWTN1FKOuw5w" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_6Jd0dWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_6LPWEGBSEfCWTN1FKOuw5w" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_6LQkMGBSEfCWTN1FKOuw5w" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_504Eg2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.platform" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_504EhGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_504EhWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_504EhmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_504Eh2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_6kY14GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_504EiGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.platform" side="Right"/>
  </children>
  <bindingTables xmi:id="_504EiWBSEfCWTN1FKOuw5w" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_504EimBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_512U52BSEfCWTN1FKOuw5w" keySequence="CTRL+1" command="_51aQGmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_513jBGBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+I" command="_51ZpKmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_513jD2BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+L" command="_51gW32BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515YMmBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+D" command="_51g9uGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_516mXGBSEfCWTN1FKOuw5w" keySequence="CTRL+V" command="_51Ya2mBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_5170eGBSEfCWTN1FKOuw5w" keySequence="CTRL+A" command="_51cFZGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_518bh2BSEfCWTN1FKOuw5w" keySequence="CTRL+C" command="_51dTfWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-QsWBSEfCWTN1FKOuw5w" keySequence="CTRL+X" command="_51a3QWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-QsmBSEfCWTN1FKOuw5w" keySequence="CTRL+Y" command="_51csbGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-QtWBSEfCWTN1FKOuw5w" keySequence="CTRL+Z" command="_51a3OWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-3yGBSEfCWTN1FKOuw5w" keySequence="ALT+PAGE_UP" command="_51dTVWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-3yWBSEfCWTN1FKOuw5w" keySequence="ALT+PAGE_DOWN" command="_51fIkmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-3zWBSEfCWTN1FKOuw5w" keySequence="SHIFT+INSERT" command="_51Ya2mBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51_e0GBSEfCWTN1FKOuw5w" keySequence="ALT+F11" command="_51ZCDmBSEfCWTN1FKOuw5w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_52BUFWBSEfCWTN1FKOuw5w" keySequence="CTRL+F10" command="_51ZB5WBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7FmBSEfCWTN1FKOuw5w" keySequence="CTRL+INSERT" command="_51dTfWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7KGBSEfCWTN1FKOuw5w" keySequence="CTRL+PAGE_UP" command="_51gWumBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7KWBSEfCWTN1FKOuw5w" keySequence="CTRL+PAGE_DOWN" command="_51aQI2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52CiIGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+F1" command="_51Zo-2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52CiIWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+F2" command="_51ehnWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52CiImBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+F3" command="_51gWrGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52CiKGBSEfCWTN1FKOuw5w" keySequence="SHIFT+DEL" command="_51a3QWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_KPljcGBTEfC_DLQ4ghLRVw" keySequence="CTRL+SPACE" command="_51fvvGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_KPnYoGBTEfC_DLQ4ghLRVw" keySequence="CTRL+SHIFT+SPACE" command="_51a3EmBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_51yDcGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.textEditorScope" bindingContext="_51koEmBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_510fsGBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+CR" command="_51gWq2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_511GwmBSEfCWTN1FKOuw5w" keySequence="CTRL+BS" command="_51Xzx2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_512U5GBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+Q" command="_51aQAmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_513jB2BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+J" command="_51ZpJGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_513jC2BSEfCWTN1FKOuw5w" keySequence="CTRL++" command="_51ehk2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514KGGBSEfCWTN1FKOuw5w" keySequence="CTRL+-" command="_51cFO2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514xLGBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+C" command="_51Xz2WBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515YOmBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+F" command="_51a3HmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515_RmBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+J" command="_51aQEWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515_T2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+A" command="_51d6cmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_516mWWBSEfCWTN1FKOuw5w" keySequence="CTRL+T" command="_51g90GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_517NZGBSEfCWTN1FKOuw5w" keySequence="CTRL+J" command="_51ZB7WBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_517NaGBSEfCWTN1FKOuw5w" keySequence="CTRL+L" command="_51fv1GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_517NcGBSEfCWTN1FKOuw5w" keySequence="CTRL+O" command="_51dThGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_5170c2BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+/" command="_51a3KmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_518bj2BSEfCWTN1FKOuw5w" keySequence="CTRL+D" command="_51ZB_WBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519pqWBSEfCWTN1FKOuw5w" keySequence="CTRL+=" command="_51ehk2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519pr2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+Y" command="_51XMsWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-QuGBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+DEL" command="_51fvw2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-QumBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+X" command="_51dThmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-Qu2BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+Y" command="_51cFNWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-Qv2BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+\" command="_51d6i2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-Qw2BSEfCWTN1FKOuw5w" keySequence="CTRL+DEL" command="_51a3MmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-3wmBSEfCWTN1FKOuw5w" keySequence="ALT+ARROW_UP" command="_51g9_WBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-3xGBSEfCWTN1FKOuw5w" keySequence="ALT+ARROW_DOWN" command="_51fIoWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-3ymBSEfCWTN1FKOuw5w" keySequence="SHIFT+END" command="_51cFRmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51_e0WBSEfCWTN1FKOuw5w" keySequence="SHIFT+HOME" command="_51beRWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51_e12BSEfCWTN1FKOuw5w" keySequence="END" command="_51gWyGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51_e2WBSEfCWTN1FKOuw5w" keySequence="INSERT" command="_51d6l2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51_e32BSEfCWTN1FKOuw5w" keySequence="F2" command="_51aQJWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52AF6WBSEfCWTN1FKOuw5w" keySequence="HOME" command="_51gW72BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52AF7GBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+ARROW_UP" command="_51g90mBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52AF8GBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+ARROW_DOWN" command="_51csSGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52As8GBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+INSERT" command="_51ZpEGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52As_GBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_51cFSmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUAWBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_51ZpF2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUFmBSEfCWTN1FKOuw5w" keySequence="CTRL+F10" command="_51gWpmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7FGBSEfCWTN1FKOuw5w" keySequence="CTRL+END" command="_51fIpWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7I2BSEfCWTN1FKOuw5w" keySequence="CTRL+ARROW_UP" command="_51Zo-GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7JGBSEfCWTN1FKOuw5w" keySequence="CTRL+ARROW_DOWN" command="_51g-EmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7JmBSEfCWTN1FKOuw5w" keySequence="CTRL+ARROW_LEFT" command="_51dTd2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7J2BSEfCWTN1FKOuw5w" keySequence="CTRL+ARROW_RIGHT" command="_51ZpL2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7KmBSEfCWTN1FKOuw5w" keySequence="CTRL+HOME" command="_51Ya2GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52CiI2BSEfCWTN1FKOuw5w" keySequence="CTRL+NUMPAD_MULTIPLY" command="_51fIt2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52CiJGBSEfCWTN1FKOuw5w" keySequence="CTRL+NUMPAD_ADD" command="_51g9vWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52CiJWBSEfCWTN1FKOuw5w" keySequence="CTRL+NUMPAD_SUBTRACT" command="_51gWqWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52CiJmBSEfCWTN1FKOuw5w" keySequence="CTRL+NUMPAD_DIVIDE" command="_51Zo_WBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52CiNWBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_51fIwGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DJNGBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_51d6mWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwVWBSEfCWTN1FKOuw5w" keySequence="SHIFT+CR" command="_51gW7mBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_KPuGUGBTEfC_DLQ4ghLRVw" keySequence="ALT+/" command="_51gW9GBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_511GwGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.contexts.window" bindingContext="_504rkGBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_511GwWBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+SHIFT+A" command="_51fIo2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_511t0GBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+SHIFT+T" command="_51ZB5GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_511t0WBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+SHIFT+L" command="_51d6imBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_511t0mBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+SHIFT+M" command="_51g9sGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_512U4GBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+Q O" command="_51fIgGBSEfCWTN1FKOuw5w">
      <parameters xmi:id="_512U4WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_512U4mBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+Q P" command="_51fIgGBSEfCWTN1FKOuw5w">
      <parameters xmi:id="_512U42BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.PackageExplorer"/>
    </bindings>
    <bindings xmi:id="_512U6GBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+B" command="_51fIiGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_512U6WBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+R" command="_51g-GGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_512U6mBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+Q Q" command="_51fIgGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_512U62BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+S" command="_51ehhmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_512U7GBSEfCWTN1FKOuw5w" keySequence="CTRL+3" command="_51aQJGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_512U7WBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+T" command="_51a3QGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_512U72BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+T" command="_51ZpAGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51278WBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+Q S" command="_51fIgGBSEfCWTN1FKOuw5w">
      <parameters xmi:id="_51278mBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_51279mBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+Q T" command="_51fIgGBSEfCWTN1FKOuw5w">
      <parameters xmi:id="_512792BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.TypeHierarchy"/>
    </bindings>
    <bindings xmi:id="_5127-GBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+U" command="_51ZpDmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_5127-2BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+V" command="_51gWzmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_5127_mBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+Q V" command="_51fIgGBSEfCWTN1FKOuw5w">
      <parameters xmi:id="_5127_2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_513jAGBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+G" command="_51ehm2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_513jAWBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+W" command="_51a3P2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_513jAmBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+H" command="_51dTb2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_513jBWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+Q H" command="_51fIgGBSEfCWTN1FKOuw5w">
      <parameters xmi:id="_513jBmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_513jCGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+Q J" command="_51fIgGBSEfCWTN1FKOuw5w">
      <parameters xmi:id="_513jCWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.JavadocView"/>
    </bindings>
    <bindings xmi:id="_513jCmBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+K" command="_51Zo9WBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_513jDGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+Q K" command="_51fIgGBSEfCWTN1FKOuw5w">
      <parameters xmi:id="_513jDWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_513jDmBSEfCWTN1FKOuw5w" keySequence="CTRL+," command="_51Ya4GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514KFWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+Q L" command="_51fIgGBSEfCWTN1FKOuw5w">
      <parameters xmi:id="_514KFmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_514KF2BSEfCWTN1FKOuw5w" keySequence="CTRL+-" command="_51gW4mBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514KGWBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+N" command="_51d6ZGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514KGmBSEfCWTN1FKOuw5w" keySequence="CTRL+." command="_51g91mBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514xIGBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+O" command="_51g9s2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514xIWBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+P" command="_51ZCEWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514xI2BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+A" command="_51fIkGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514xJWBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+B" command="_51Zo9mBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514xKGBSEfCWTN1FKOuw5w" keySequence="CTRL+#" command="_51ZB5mBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515YNGBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+T" command="_51dTeGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515YNWBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+E" command="_51ZpCGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515YP2BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+G" command="_51g95mBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515YQmBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+H" command="_51Ya9mBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515_QmBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+Q X" command="_51fIgGBSEfCWTN1FKOuw5w">
      <parameters xmi:id="_515_Q2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_515_RGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+Q Y" command="_51fIgGBSEfCWTN1FKOuw5w">
      <parameters xmi:id="_515_RWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_515_R2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+Q Z" command="_51fIgGBSEfCWTN1FKOuw5w">
      <parameters xmi:id="_515_SGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_515_TWBSEfCWTN1FKOuw5w" keySequence="CTRL+P" command="_51gWoGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515_TmBSEfCWTN1FKOuw5w" keySequence="CTRL+Q" command="_51gWsmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_516mU2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+C" command="_51fvwmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_516mVmBSEfCWTN1FKOuw5w" keySequence="CTRL+S" command="_51cFPWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_516mWmBSEfCWTN1FKOuw5w" keySequence="CTRL+U" command="_51csZWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_516mW2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+F" command="_51gWt2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_516mXWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+G" command="_51ehfGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_516mX2BSEfCWTN1FKOuw5w" keySequence="CTRL+W" command="_51dTUGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_517NYGBSEfCWTN1FKOuw5w" keySequence="CTRL+H" command="_51fvu2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_517NZWBSEfCWTN1FKOuw5w" keySequence="CTRL+K" command="_51fIjGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_517NaWBSEfCWTN1FKOuw5w" keySequence="CTRL+M" command="_51fvt2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_517NbGBSEfCWTN1FKOuw5w" keySequence="CTRL+N" command="_51g962BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_5170d2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+P" command="_51ehoWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_5170e2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+R" command="_51cscWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_518bgGBSEfCWTN1FKOuw5w" keySequence="CTRL+B" command="_51Ya5GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_518bgWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+Q B" command="_51fIgGBSEfCWTN1FKOuw5w">
      <parameters xmi:id="_518bgmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_518biWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+S" command="_51dTZGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_518bjGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+T" command="_51d6dWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_518bjWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+Q C" command="_51fIgGBSEfCWTN1FKOuw5w">
      <parameters xmi:id="_518bjmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_518bkGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+Q D" command="_51fIgGBSEfCWTN1FKOuw5w">
      <parameters xmi:id="_518bkWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.SourceView"/>
    </bindings>
    <bindings xmi:id="_519Ck2BSEfCWTN1FKOuw5w" keySequence="CTRL+E" command="_51a3L2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519ClGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+V" command="_51cFSWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519ClmBSEfCWTN1FKOuw5w" keySequence="CTRL+F" command="_51ZCB2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519CmWBSEfCWTN1FKOuw5w" keySequence="CTRL+G" command="_51XzyGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519Cm2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+W" command="_51g90WBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519CnGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+H" command="_51a3JmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519poGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+I" command="_51ZB52BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519po2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+J" command="_51a3LGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519ppmBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+L" command="_51aQFWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519pp2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+M" command="_51g9vmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519pqGBSEfCWTN1FKOuw5w" keySequence="CTRL+=" command="_51beNGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519pqmBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+N" command="_51a3OmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519psGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+Z" command="_51dTamBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-QtmBSEfCWTN1FKOuw5w" keySequence="CTRL+_" command="_51a3FmBSEfCWTN1FKOuw5w">
      <parameters xmi:id="_51-Qt2BSEfCWTN1FKOuw5w" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_51-QvGBSEfCWTN1FKOuw5w" keySequence="CTRL+{" command="_51a3FmBSEfCWTN1FKOuw5w">
      <parameters xmi:id="_51-QvWBSEfCWTN1FKOuw5w" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_51-QwmBSEfCWTN1FKOuw5w" keySequence="CTRL+DEL" command="_51gW5mBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-3xWBSEfCWTN1FKOuw5w" keySequence="ALT+ARROW_LEFT" command="_51ZB6WBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-3x2BSEfCWTN1FKOuw5w" keySequence="ALT+ARROW_RIGHT" command="_51beMWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-3z2BSEfCWTN1FKOuw5w" keySequence="SHIFT+F2" command="_51ehdGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-30WBSEfCWTN1FKOuw5w" keySequence="SHIFT+F5" command="_51csTmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-30mBSEfCWTN1FKOuw5w" keySequence="ALT+F7" command="_51d6fWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51_e0mBSEfCWTN1FKOuw5w" keySequence="ALT+F5" command="_51cFW2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51_e1WBSEfCWTN1FKOuw5w" keySequence="F11" command="_51gXAWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51_e1mBSEfCWTN1FKOuw5w" keySequence="F12" command="_51fvv2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51_e3mBSEfCWTN1FKOuw5w" keySequence="F2" command="_51Ya42BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51_e4WBSEfCWTN1FKOuw5w" keySequence="F3" command="_51aQE2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52AF4GBSEfCWTN1FKOuw5w" keySequence="F4" command="_51Ya7WBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52AF52BSEfCWTN1FKOuw5w" keySequence="F5" command="_51beO2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52AF6mBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+F7" command="_51g9sWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52AF62BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+F8" command="_51a3FWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52AF72BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+F9" command="_51cFV2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52AF8mBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+ARROW_LEFT" command="_51gWsmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52AF82BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+F11" command="_51dTZ2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52AF9WBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+ARROW_RIGHT" command="_51Zo_2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52AF92BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+F12" command="_51Xz42BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52As8WBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+F4" command="_51a3P2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52As9WBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+F6" command="_51ehkGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUAGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+X J" command="_51fIl2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUAmBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+X M" command="_51csRGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUA2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+X A" command="_51Ya3mBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUBGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+X E" command="_51ehmmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUBWBSEfCWTN1FKOuw5w" keySequence="CTRL+F7" command="_51dTfmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUC2BSEfCWTN1FKOuw5w" keySequence="CTRL+F8" command="_51aQHWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUEWBSEfCWTN1FKOuw5w" keySequence="CTRL+F9" command="_51ZpDWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUF2BSEfCWTN1FKOuw5w" keySequence="CTRL+F11" command="_51gWzGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7E2BSEfCWTN1FKOuw5w" keySequence="CTRL+F12" command="_51Zo92BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7HGBSEfCWTN1FKOuw5w" keySequence="CTRL+F4" command="_51dTUGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7IGBSEfCWTN1FKOuw5w" keySequence="CTRL+F6" command="_51ZCEmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7IWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+F7" command="_51fIp2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7ImBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+X G" command="_51gW_GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52CiJ2BSEfCWTN1FKOuw5w" keySequence="SHIFT+DEL" command="_51aQPWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52CiKWBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+SHIFT+ARROW_UP" command="_51d6pmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52CiKmBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+SHIFT+ARROW_DOWN" command="_51g9-GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52CiK2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+X X" command="_51d6aWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52CiLGBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+SHIFT+ARROW_RIGHT" command="_51d6kmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52CiLWBSEfCWTN1FKOuw5w" keySequence="CTRL+BREAK" command="_51ZB92BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52CiLmBSEfCWTN1FKOuw5w" keySequence="ALT+X" command="_51dTaWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52CiMGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+X O" command="_51ehdWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52CiM2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+X P" command="_51g9xmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52CiNGBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_51ehhWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DJMGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+X Q" command="_51ZpJ2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DJMWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+X R" command="_51csa2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DJMmBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+X T" command="_51cFYWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DJM2BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_51a3GGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DJNWBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+SHIFT+F12" command="_51g9wGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DJNmBSEfCWTN1FKOuw5w" keySequence="DEL" command="_51Zo8GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DJOWBSEfCWTN1FKOuw5w" keySequence="ALT+C" command="_51a3JGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwRGBSEfCWTN1FKOuw5w" keySequence="ALT+V" command="_51csQ2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwUGBSEfCWTN1FKOuw5w" keySequence="ALT+-" command="_51d6Y2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwUmBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+E E" command="_51ZB62BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwU2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+E G" command="_51beImBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwVGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+E J" command="_51Ya2WBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwVmBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+E S" command="_51cFZWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwV2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+E T" command="_51ZB4mBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52EXUGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+E L" command="_51Ya4WBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52EXUWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+E N" command="_51g922BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52EXUmBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+E P" command="_51XzwmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52EXVWBSEfCWTN1FKOuw5w" keySequence="ALT+CR" command="_51fvp2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52EXVmBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+E R" command="_51aQBWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52EXV2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+D E" command="_51g-AmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52EXWGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+D A" command="_51fv12BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52EXWWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+D R" command="_51csbWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52EXWmBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+D T" command="_51Xz1WBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52EXW2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+D X" command="_51csYmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52EXXGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+D J" command="_51fIxWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52EXXWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+D O" command="_51dTg2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52EXXmBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+D P" command="_51fv7mBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52EXX2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+D Q" command="_51csVWBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_511Gw2BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_51koQWBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_511GxGBSEfCWTN1FKOuw5w" keySequence="CTRL+CR" command="_51a3ImBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_516mVGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+C" command="_51dTYmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_5170f2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+R" command="_51cFSGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519CkGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+U" command="_51fIsWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519poWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+I" command="_51cFPGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-3wGBSEfCWTN1FKOuw5w" keySequence="ALT+ARROW_UP" command="_51ehpmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-3w2BSEfCWTN1FKOuw5w" keySequence="ALT+ARROW_DOWN" command="_51a3KGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-3y2BSEfCWTN1FKOuw5w" keySequence="SHIFT+INSERT" command="_51ZCFGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51_e2GBSEfCWTN1FKOuw5w" keySequence="INSERT" command="_51cFMmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52AF4mBSEfCWTN1FKOuw5w" keySequence="F4" command="_51ZB4WBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUCWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+ARROW_UP" command="_51fvrmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUD2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+ARROW_DOWN" command="_51cFQmBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_511t02BSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.SQLEditorScope" bindingContext="_51koGGBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_511t1GBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+P" command="_51fIw2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_512782BSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+D" command="_51a3K2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514KG2BSEfCWTN1FKOuw5w" keySequence="CTRL+/" command="_51Xz3mBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514xJmBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+R" command="_51gWy2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-QuWBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+X" command="_51cscGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52CiL2BSEfCWTN1FKOuw5w" keySequence="ALT+X" command="_51g-BmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DJOmBSEfCWTN1FKOuw5w" keySequence="ALT+C" command="_51aQLmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DJRGBSEfCWTN1FKOuw5w" keySequence="ALT+Q" command="_51fv7GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwQWBSEfCWTN1FKOuw5w" keySequence="ALT+S" command="_51csR2BSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_511t1WBSEfCWTN1FKOuw5w" elementId="org.eclipse.emf.codegen.ui.jetEditorScope" bindingContext="_51koKGBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_511t1mBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+P" command="_51Xz0GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515YOGBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+F" command="_51g9yWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_5170emBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+R" command="_51cFPmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_518biGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+S" command="_51cFTmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_518bi2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+T" command="_51g9-2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519ppWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+L" command="_51aQFWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUBmBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+ARROW_UP" command="_51csVGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUDGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+ARROW_DOWN" command="_51Zo-WBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUEmBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+ARROW_LEFT" command="_51beOWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUGGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_51ZB-GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7EmBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+PAGE_UP" command="_51fvqWBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_511t12BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.javaEditorScope" bindingContext="_51koG2BSEfCWTN1FKOuw5w">
    <bindings xmi:id="_511t2GBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+P" command="_51fIpmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_512U7mBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+T" command="_51a3QGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_5128AGBSEfCWTN1FKOuw5w" keySequence="CTRL+7" command="_51d6Z2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514KEGBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+M" command="_51aQJmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514KHGBSEfCWTN1FKOuw5w" keySequence="CTRL+/" command="_51d6Z2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514xKWBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+C" command="_51d6Z2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515YOWBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+F" command="_51gW7WBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_516mUGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+B" command="_51g-AWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_516mWGBSEfCWTN1FKOuw5w" keySequence="CTRL+T" command="_51d6cGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_517NYWBSEfCWTN1FKOuw5w" keySequence="CTRL+I" command="_51beL2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_517Nb2BSEfCWTN1FKOuw5w" keySequence="CTRL+O" command="_51cFZmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_5170cmBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+/" command="_51csZmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_5170fGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+R" command="_51cscWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_518bkmBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+U" command="_51fvlmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519Cl2BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+'" command="_51d6hGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519prGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+O" command="_51a3NGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-QvmBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+\" command="_51ZB_mBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52As9mBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+ARROW_UP" command="_51dTUmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52As-WBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_51csQWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUB2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+ARROW_UP" command="_51csVGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUDWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+ARROW_DOWN" command="_51Zo-WBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUE2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+ARROW_LEFT" command="_51beOWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7EGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_51ZB-GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7GmBSEfCWTN1FKOuw5w" keySequence="CTRL+F3" command="_51g91GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DJP2BSEfCWTN1FKOuw5w" keySequence="CTRL+2 F" command="_51g9vGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwSmBSEfCWTN1FKOuw5w" keySequence="CTRL+2 R" command="_51fvq2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwTGBSEfCWTN1FKOuw5w" keySequence="CTRL+2 T" command="_51d6ZmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwTWBSEfCWTN1FKOuw5w" keySequence="CTRL+2 L" command="_51ZB8GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwT2BSEfCWTN1FKOuw5w" keySequence="CTRL+2 M" command="_51beP2BSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_511t2WBSEfCWTN1FKOuw5w" elementId="org.eclipse.core.runtime.xml" bindingContext="_51koIWBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_511t2mBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+P" command="_51fvwGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515YM2BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+D" command="_51csZGBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_511t22BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_51koNmBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_511t3GBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+P" command="_51beU2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515YQGBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+G" command="_51gWx2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515_QGBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+H" command="_51fvyWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_5170fmBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+R" command="_51Ya42BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51_e4mBSEfCWTN1FKOuw5w" keySequence="F3" command="_51gWs2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52AF4WBSEfCWTN1FKOuw5w" keySequence="F4" command="_51XzxGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUCGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+ARROW_UP" command="_51d6dmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUDmBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+ARROW_DOWN" command="_51d6e2BSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_511t3WBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.javaEditorScope" bindingContext="_51koE2BSEfCWTN1FKOuw5w">
    <bindings xmi:id="_511t3mBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+P" command="_51ehiGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51278GBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+T" command="_51csXGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_5128A2BSEfCWTN1FKOuw5w" keySequence="CTRL+7" command="_51d6kWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514KHmBSEfCWTN1FKOuw5w" keySequence="CTRL+/" command="_51d6kWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514xLWBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+C" command="_51d6kWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515YPWBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+F" command="_51ZB9GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_517NYmBSEfCWTN1FKOuw5w" keySequence="CTRL+I" command="_51cFS2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_5170cGBSEfCWTN1FKOuw5w" keySequence="CTRL+O" command="_51Xz62BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_5170dGBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+/" command="_51csWmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519CkmBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+U" command="_51a3PmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519prWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+O" command="_51beR2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-QwGBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+\" command="_51ZCFmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52As92BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+ARROW_UP" command="_51hkwGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52As-mBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_51ZpD2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7G2BSEfCWTN1FKOuw5w" keySequence="CTRL+F3" command="_51dTgmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DJQGBSEfCWTN1FKOuw5w" keySequence="CTRL+2 F" command="_51beVGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwS2BSEfCWTN1FKOuw5w" keySequence="CTRL+2 R" command="_51aQKWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwTmBSEfCWTN1FKOuw5w" keySequence="CTRL+2 L" command="_51g91WBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_511t32BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" bindingContext="_51koHWBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_511t4GBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+P" command="_51d6c2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514xJGBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+A" command="_51g-E2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515YMGBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+C" command="_51gW62BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515YPmBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+F" command="_51g952BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515_TGBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+>" command="_51fv2GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_517NY2BSEfCWTN1FKOuw5w" keySequence="CTRL+I" command="_51gWpGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_5170cWBSEfCWTN1FKOuw5w" keySequence="CTRL+O" command="_51d6g2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_5170dWBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+/" command="_51d6YmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-QwWBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+\" command="_51ehnGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51_e5GBSEfCWTN1FKOuw5w" keySequence="F3" command="_51d6b2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52As-GBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+ARROW_UP" command="_51ZpImBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52As-2BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_51csW2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUCmBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+ARROW_UP" command="_51fInGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUEGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+ARROW_DOWN" command="_51fIs2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52BUFGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+ARROW_LEFT" command="_51Ya9GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7EWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_51ehd2BSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_512U5WBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.classFileEditorScope" bindingContext="_51koGWBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_512U5mBSEfCWTN1FKOuw5w" keySequence="CTRL+1" command="_51g942BSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_51279GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.serverViewScope" bindingContext="_51koNWBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_51279WBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+D" command="_51fIkWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514xImBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+P" command="_51fIrWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514xJ2BSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+R" command="_51g92GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515YMWBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+S" command="_51aQDGBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_5127-WBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.javascriptViewScope" bindingContext="_51koRWBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_5127-mBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+U" command="_51ZpLmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_513jA2BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+H" command="_51g-FWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515YQWBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+G" command="_51d6eWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515_QWBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+H" command="_51fvr2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519CmmBSEfCWTN1FKOuw5w" keySequence="CTRL+G" command="_51fIjmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519ppGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+J" command="_51ehcGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-QsGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+Z" command="_51cFWWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-30GBSEfCWTN1FKOuw5w" keySequence="SHIFT+F2" command="_51fvy2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51_e42BSEfCWTN1FKOuw5w" keySequence="F3" command="_51fIqmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52AF42BSEfCWTN1FKOuw5w" keySequence="F4" command="_51csTWBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_5127_GBSEfCWTN1FKOuw5w" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_51koEGBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_5127_WBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+V" command="_51beUWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514xK2BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+C" command="_51fvnGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-3wWBSEfCWTN1FKOuw5w" keySequence="ALT+ARROW_UP" command="_51XzxWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-3xmBSEfCWTN1FKOuw5w" keySequence="ALT+ARROW_RIGHT" command="_51gXAGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-3zGBSEfCWTN1FKOuw5w" keySequence="SHIFT+INSERT" command="_51beUWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7FWBSEfCWTN1FKOuw5w" keySequence="CTRL+INSERT" command="_51fvnGBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_5128AWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.propertiesEditorScope" bindingContext="_51koSGBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_5128AmBSEfCWTN1FKOuw5w" keySequence="CTRL+7" command="_51d6Z2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514KHWBSEfCWTN1FKOuw5w" keySequence="CTRL+/" command="_51d6Z2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_514xKmBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+C" command="_51d6Z2BSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_514KEWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jst.jsp.ui.structured.text.editor.jsp.scope" bindingContext="_51koLWBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_514KEmBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+M" command="_51aQNmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_5170fWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+R" command="_51g9xGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519ClWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+V" command="_51csQGBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_514KE2BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_51koQmBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_514KFGBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+M" command="_51Xz6mBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_516mVWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+C" command="_51dTYmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_517NcWBSEfCWTN1FKOuw5w" keySequence="CTRL+O" command="_51g9ymBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_5170gGBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+R" command="_51cFSGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_518bimBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+S" command="_51beIWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519CkWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+U" command="_51fIsWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519pomBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+I" command="_51cFPGBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_515YNmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ant.ui.AntEditorScope" bindingContext="_51koJ2BSEfCWTN1FKOuw5w">
    <bindings xmi:id="_515YN2BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+F" command="_51gW7WBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_5170eWBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+R" command="_51Ya7GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519pq2BSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+O" command="_51Xz0WBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51-3zmBSEfCWTN1FKOuw5w" keySequence="SHIFT+F2" command="_51d6hmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51_e4GBSEfCWTN1FKOuw5w" keySequence="F3" command="_51Xz2GBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_515YO2BSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.pdeEditorContext" bindingContext="_51koM2BSEfCWTN1FKOuw5w">
    <bindings xmi:id="_515YPGBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+F" command="_51ZB9mBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_517NdGBSEfCWTN1FKOuw5w" keySequence="CTRL+O" command="_51ZCDWBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_515_SWBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_51koF2BSEfCWTN1FKOuw5w">
    <bindings xmi:id="_515_SmBSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+M" command="_51cFXmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_515_S2BSEfCWTN1FKOuw5w" keySequence="ALT+CTRL+N" command="_51g9wWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_516mV2BSEfCWTN1FKOuw5w" keySequence="CTRL+T" command="_51aQKmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_516mXmBSEfCWTN1FKOuw5w" keySequence="CTRL+W" command="_51d6n2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_517Na2BSEfCWTN1FKOuw5w" keySequence="CTRL+N" command="_51ehlmBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_515_UGBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.debugging" bindingContext="_51koN2BSEfCWTN1FKOuw5w">
    <bindings xmi:id="_515_UWBSEfCWTN1FKOuw5w" keySequence="CTRL+R" command="_51dTgWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51_e02BSEfCWTN1FKOuw5w" keySequence="F7" command="_51g93GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_51_e1GBSEfCWTN1FKOuw5w" keySequence="F8" command="_51d6kGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52AF5mBSEfCWTN1FKOuw5w" keySequence="F5" command="_51Ya8WBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52AF6GBSEfCWTN1FKOuw5w" keySequence="F6" command="_51cFT2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7GWBSEfCWTN1FKOuw5w" keySequence="CTRL+F2" command="_51fvxmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7HWBSEfCWTN1FKOuw5w" keySequence="CTRL+F5" command="_51gW-2BSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_516mUWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" bindingContext="_51koGmBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_516mUmBSEfCWTN1FKOuw5w" keySequence="ALT+SHIFT+B" command="_51g-AWBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_517NZmBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_51koOGBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_517NZ2BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+," command="_51gWtmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_517NamBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+." command="_51fvtWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_519CmGBSEfCWTN1FKOuw5w" keySequence="CTRL+G" command="_51fvtmBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_517NbWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.DiffViewer" bindingContext="_51koFGBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_517NbmBSEfCWTN1FKOuw5w" keySequence="CTRL+O" command="_51csU2BSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_517NcmBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_51koRGBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_517Nc2BSEfCWTN1FKOuw5w" keySequence="CTRL+O" command="_51Ya0mBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_518bg2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_51koSWBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_518bhGBSEfCWTN1FKOuw5w" keySequence="CTRL+C" command="_51aQMmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7JWBSEfCWTN1FKOuw5w" keySequence="CTRL+ARROW_LEFT" command="_51ZB-mBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_518bhWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_51koPGBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_518bhmBSEfCWTN1FKOuw5w" keySequence="CTRL+C" command="_51ZCE2BSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_51-Qs2BSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.console" bindingContext="_51koMGBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_51-QtGBSEfCWTN1FKOuw5w" keySequence="CTRL+Z" command="_51g9zWBSEfCWTN1FKOuw5w">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_51_e2mBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_51koQ2BSEfCWTN1FKOuw5w">
    <bindings xmi:id="_51_e22BSEfCWTN1FKOuw5w" keySequence="F1" command="_51Xz1mBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_51_e3GBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" bindingContext="_51koSmBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_51_e3WBSEfCWTN1FKOuw5w" keySequence="F2" command="_51Zo8mBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_52AF5GBSEfCWTN1FKOuw5w" elementId="org.eclipse.buildship.ui.contexts.taskview" bindingContext="_51koRmBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_52AF5WBSEfCWTN1FKOuw5w" keySequence="F5" command="_51gW6mBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_52AF7WBSEfCWTN1FKOuw5w" elementId="org.eclipse.jst.pagedesigner.editorContext" bindingContext="_51koK2BSEfCWTN1FKOuw5w">
    <bindings xmi:id="_52AF7mBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+F9" command="_51cFTWBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52AF8WBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+F10" command="_51g9zmBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52AF9GBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+F11" command="_51gW_2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52AF9mBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+F12" command="_51g94GBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52As9GBSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+F5" command="_51fIp2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7H2BSEfCWTN1FKOuw5w" keySequence="CTRL+F5" command="_51d6fWBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_52As8mBSEfCWTN1FKOuw5w" elementId="org.eclipse.jst.jsf.facesconfig.editorContext" bindingContext="_51koS2BSEfCWTN1FKOuw5w">
    <bindings xmi:id="_52As82BSEfCWTN1FKOuw5w" keySequence="CTRL+SHIFT+F5" command="_51fIp2BSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52B7HmBSEfCWTN1FKOuw5w" keySequence="CTRL+F5" command="_51d6fWBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_52B7F2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_51koNGBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_52B7GGBSEfCWTN1FKOuw5w" keySequence="CTRL+INSERT" command="_51ehgWBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_52CiMWBSEfCWTN1FKOuw5w" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_51koPWBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_52CiMmBSEfCWTN1FKOuw5w" keySequence="ALT+Y" command="_51d6YGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DJN2BSEfCWTN1FKOuw5w" keySequence="ALT+A" command="_51d6YGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DJOGBSEfCWTN1FKOuw5w" keySequence="ALT+B" command="_51d6YGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DJO2BSEfCWTN1FKOuw5w" keySequence="ALT+C" command="_51d6YGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DJPGBSEfCWTN1FKOuw5w" keySequence="ALT+D" command="_51d6YGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DJPWBSEfCWTN1FKOuw5w" keySequence="ALT+E" command="_51d6YGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DJPmBSEfCWTN1FKOuw5w" keySequence="ALT+F" command="_51d6YGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DJQWBSEfCWTN1FKOuw5w" keySequence="ALT+G" command="_51d6YGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DJQ2BSEfCWTN1FKOuw5w" keySequence="ALT+P" command="_51d6YGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwQGBSEfCWTN1FKOuw5w" keySequence="ALT+R" command="_51d6YGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwQmBSEfCWTN1FKOuw5w" keySequence="ALT+S" command="_51d6YGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwQ2BSEfCWTN1FKOuw5w" keySequence="ALT+T" command="_51d6YGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwRWBSEfCWTN1FKOuw5w" keySequence="ALT+V" command="_51d6YGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwRmBSEfCWTN1FKOuw5w" keySequence="ALT+W" command="_51d6YGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwR2BSEfCWTN1FKOuw5w" keySequence="ALT+H" command="_51d6YGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwSGBSEfCWTN1FKOuw5w" keySequence="ALT+L" command="_51d6YGBSEfCWTN1FKOuw5w"/>
    <bindings xmi:id="_52DwSWBSEfCWTN1FKOuw5w" keySequence="ALT+N" command="_51d6YGBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_52EXU2BSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.BreakpointView" bindingContext="_51koEWBSEfCWTN1FKOuw5w">
    <bindings xmi:id="_52EXVGBSEfCWTN1FKOuw5w" keySequence="ALT+CR" command="_51dTbmBSEfCWTN1FKOuw5w"/>
  </bindingTables>
  <bindingTables xmi:id="_6MzcUWBSEfCWTN1FKOuw5w" bindingContext="_6MzcUGBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6MzcU2BSEfCWTN1FKOuw5w" bindingContext="_6MzcUmBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M0DYWBSEfCWTN1FKOuw5w" bindingContext="_6M0DYGBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M0DY2BSEfCWTN1FKOuw5w" bindingContext="_6M0DYmBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M0DZWBSEfCWTN1FKOuw5w" bindingContext="_6M0DZGBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M0DZ2BSEfCWTN1FKOuw5w" bindingContext="_6M0DZmBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M0qcWBSEfCWTN1FKOuw5w" bindingContext="_6M0qcGBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M0qc2BSEfCWTN1FKOuw5w" bindingContext="_6M0qcmBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M0qdWBSEfCWTN1FKOuw5w" bindingContext="_6M0qdGBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M0qd2BSEfCWTN1FKOuw5w" bindingContext="_6M0qdmBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M0qeWBSEfCWTN1FKOuw5w" bindingContext="_6M0qeGBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M1RgWBSEfCWTN1FKOuw5w" bindingContext="_6M1RgGBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M1Rg2BSEfCWTN1FKOuw5w" bindingContext="_6M1RgmBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M1RhWBSEfCWTN1FKOuw5w" bindingContext="_6M1RhGBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M1Rh2BSEfCWTN1FKOuw5w" bindingContext="_6M1RhmBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M14kGBSEfCWTN1FKOuw5w" bindingContext="_6M1RiGBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M14kmBSEfCWTN1FKOuw5w" bindingContext="_6M14kWBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M14lGBSEfCWTN1FKOuw5w" bindingContext="_6M14k2BSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M2foWBSEfCWTN1FKOuw5w" bindingContext="_6M2foGBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M2fo2BSEfCWTN1FKOuw5w" bindingContext="_6M2fomBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M2fpWBSEfCWTN1FKOuw5w" bindingContext="_6M2fpGBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M2fp2BSEfCWTN1FKOuw5w" bindingContext="_6M2fpmBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M2fqWBSEfCWTN1FKOuw5w" bindingContext="_6M2fqGBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M3GsWBSEfCWTN1FKOuw5w" bindingContext="_6M3GsGBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M3Gs2BSEfCWTN1FKOuw5w" bindingContext="_6M3GsmBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M3GtWBSEfCWTN1FKOuw5w" bindingContext="_6M3GtGBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M3Gt2BSEfCWTN1FKOuw5w" bindingContext="_6M3GtmBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M3twWBSEfCWTN1FKOuw5w" bindingContext="_6M3twGBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M3tw2BSEfCWTN1FKOuw5w" bindingContext="_6M3twmBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M3txWBSEfCWTN1FKOuw5w" bindingContext="_6M3txGBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M3tx2BSEfCWTN1FKOuw5w" bindingContext="_6M3txmBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M4U0WBSEfCWTN1FKOuw5w" bindingContext="_6M4U0GBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M4U02BSEfCWTN1FKOuw5w" bindingContext="_6M4U0mBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M4U1WBSEfCWTN1FKOuw5w" bindingContext="_6M4U1GBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M4U12BSEfCWTN1FKOuw5w" bindingContext="_6M4U1mBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M4U2WBSEfCWTN1FKOuw5w" bindingContext="_6M4U2GBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M474WBSEfCWTN1FKOuw5w" bindingContext="_6M474GBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M4742BSEfCWTN1FKOuw5w" bindingContext="_6M474mBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M475WBSEfCWTN1FKOuw5w" bindingContext="_6M475GBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M5i8WBSEfCWTN1FKOuw5w" bindingContext="_6M5i8GBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M5i82BSEfCWTN1FKOuw5w" bindingContext="_6M5i8mBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M5i9WBSEfCWTN1FKOuw5w" bindingContext="_6M5i9GBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M5i92BSEfCWTN1FKOuw5w" bindingContext="_6M5i9mBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M6KAWBSEfCWTN1FKOuw5w" bindingContext="_6M6KAGBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M6KA2BSEfCWTN1FKOuw5w" bindingContext="_6M6KAmBSEfCWTN1FKOuw5w"/>
  <bindingTables xmi:id="_6M6KBWBSEfCWTN1FKOuw5w" bindingContext="_6M6KBGBSEfCWTN1FKOuw5w"/>
  <rootContext xmi:id="_504EimBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_504rkGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_504rkWBSEfCWTN1FKOuw5w" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_51koEGBSEfCWTN1FKOuw5w" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_51koEWBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_51koEmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_51koE2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.javaEditorScope" name="Editing JavaScript Source" description="Editing JavaScript Source Context">
          <children xmi:id="_51koRWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.javascriptViewScope" name="JavaScript View" description="JavaScript View Context"/>
        </children>
        <children xmi:id="_51koFGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.DiffViewer" name="In Diff Viewer"/>
        <children xmi:id="_51koGGBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.SQLEditorScope" name="Editing SQL" description="Editing SQL Context"/>
        <children xmi:id="_51koGWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.classFileEditorScope" name="Browsing attached Java Source" description="Browsing attached Java Source Context"/>
        <children xmi:id="_51koG2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.javaEditorScope" name="Editing Java Source" description="Editing Java Source Context"/>
        <children xmi:id="_51koHWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors">
          <children xmi:id="_51koHmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xml.cleanup" name="XML Source Cleanup" description="XML Source Cleanup"/>
          <children xmi:id="_51koH2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.sse.comments" name="Source Comments in Structured Text Editors" description="Source Comments in Structured Text Editors"/>
          <children xmi:id="_51koIGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jst.jsp.core.jspsource" name="JSP Source" description="JSP Source"/>
          <children xmi:id="_51koIWBSEfCWTN1FKOuw5w" elementId="org.eclipse.core.runtime.xml" name="Editing XML Source" description="Editing XML Source"/>
          <children xmi:id="_51koImBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xml.occurrences" name="XML Source Occurrences" description="XML Source Occurrences"/>
          <children xmi:id="_51koI2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xml.grammar" name="XML Source Grammar" description="XML Source Grammar"/>
          <children xmi:id="_51koJGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xml.comments" name="XML Source Comments" description="XML Source Comments"/>
          <children xmi:id="_51koJmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xml.expand" name="XML Source Expand/Collapse" description="XML Source Expand/Collapse"/>
          <children xmi:id="_51koKWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.sse.hideFormat" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors"/>
          <children xmi:id="_51koLGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xml.selection" name="XML Source Selection" description="XML Source Selection"/>
          <children xmi:id="_51koLWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jst.jsp.ui.structured.text.editor.jsp.scope" name="Editing JSP Source" description="Editing JSP Source"/>
          <children xmi:id="_51koMmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xml.navigation" name="XML Source Navigation" description="XML Source Navigation"/>
          <children xmi:id="_51koPmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.css.core.csssource" name="Editing CSS Source" description="Editing CSS Source"/>
          <children xmi:id="_51koQGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.html.core.htmlsource" name="Editing HTML Source" description="Editing HTML Source"/>
          <children xmi:id="_51koR2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xml.dependencies" name="XML Source Dependencies" description="XML Source Dependencies"/>
          <children xmi:id="_51koTGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.html.occurrences" name="HTML Source Occurrences" description="HTML Source Occurrences"/>
        </children>
        <children xmi:id="_51koJ2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ant.ui.AntEditorScope" name="Editing Ant Buildfiles" description="Editing Ant Buildfiles Context"/>
        <children xmi:id="_51koKGBSEfCWTN1FKOuw5w" elementId="org.eclipse.emf.codegen.ui.jetEditorScope" name="Editing JET Source" description="Editing JET Source Context"/>
        <children xmi:id="_51koK2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jst.pagedesigner.editorContext" name="Using Web Page Editor" description="Key binding context when using the web page editor"/>
        <children xmi:id="_51koM2BSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.pdeEditorContext" name="PDE editor" description="The context used by PDE editors"/>
        <children xmi:id="_51koNmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_51koP2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xsd.ui.text.editor.context" name="Editing XSD context"/>
        <children xmi:id="_51koQmBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_51koQ2BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_51koRGBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
        </children>
        <children xmi:id="_51koSGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.propertiesEditorScope" name="Editing Properties Files" description="Editing Properties Files Context"/>
        <children xmi:id="_51koS2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jst.jsf.facesconfig.editorContext" name="In Faces Config Editor" description="Key binding context when using the Faces Config Editor"/>
      </children>
      <children xmi:id="_51koFWBSEfCWTN1FKOuw5w" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_51koF2BSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_51koHGBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.schemaobjecteditor.schemaediting" name="Schema Object Editor" description="Schema Object Editor"/>
      <children xmi:id="_51koMGBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_51koMWBSEfCWTN1FKOuw5w" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_51koNGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_51koNWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.serverViewScope" name="In Servers View" description="In Servers View"/>
      <children xmi:id="_51koN2BSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_51koOGBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_51koOWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xsl.debug.ui.context" name="XSLT Debugging" description="Context for debugging XSLT"/>
        <children xmi:id="_51koOmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.debug.ui.debugging" name="Debugging Java" description="Debugging Java programs"/>
      </children>
      <children xmi:id="_51koPGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_51koPWBSEfCWTN1FKOuw5w" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_51koQWBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_51koRmBSEfCWTN1FKOuw5w" elementId="org.eclipse.buildship.ui.contexts.taskview" name="In Gradle Tasks View" description="This context is activated when the Gradle Tasks view is in focus"/>
      <children xmi:id="_51koSWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View">
        <children xmi:id="_51koSmBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" name="In Git Repositories View"/>
      </children>
    </children>
    <children xmi:id="_504rkmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
  </rootContext>
  <rootContext xmi:id="_51koFmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_51koGmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" name="Editor Breadcrumb Navigation" description="Editor Breadcrumb Navigation Context"/>
  <rootContext xmi:id="_51koJWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xsd.ui.editor.sourceView" name="XSD Editor Source View" description="XSD Editor Source View"/>
  <rootContext xmi:id="_51koKmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.wsdl.ui.editor.sourceView" name="WSDL Editor Source View" description="WSDL Editor Source View"/>
  <rootContext xmi:id="_51koLmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xsd.ui.editor.designView" name="XSD Editor Design View" description="XSD Editor Design View"/>
  <rootContext xmi:id="_51koL2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_51koO2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.wsdl.ui.editor.designView" name="WSDL Editor Design View" description="WSDL Editor Design View"/>
  <rootContext xmi:id="_6MzcUGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ant.ui.actionSet.presentation" name="Auto::org.eclipse.ant.ui.actionSet.presentation"/>
  <rootContext xmi:id="_6MzcUmBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.sqlscrapbook.actionSet" name="Auto::org.eclipse.datatools.sqltools.sqlscrapbook.actionSet"/>
  <rootContext xmi:id="_6M0DYGBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_6M0DYmBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_6M0DZGBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_6M0DZmBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_6M0qcGBSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.CoverageActionSet" name="Auto::org.eclipse.eclemma.ui.CoverageActionSet"/>
  <rootContext xmi:id="_6M0qcmBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_6M0qdGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_6M0qdmBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_6M0qeGBSEfCWTN1FKOuw5w" elementId="org.eclipse.emf.codegen.ui.jet.actionSet" name="Auto::org.eclipse.emf.codegen.ui.jet.actionSet"/>
  <rootContext xmi:id="_6M1RgGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.debug.ui.JDTDebugActionSet" name="Auto::org.eclipse.jdt.debug.ui.JDTDebugActionSet"/>
  <rootContext xmi:id="_6M1RgmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.junit.JUnitActionSet" name="Auto::org.eclipse.jdt.junit.JUnitActionSet"/>
  <rootContext xmi:id="_6M1RhGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.jdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_6M1RhmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.jdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_6M1RiGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.JavaActionSet" name="Auto::org.eclipse.jdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_6M14kWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.A_OpenActionSet" name="Auto::org.eclipse.jdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_6M14k2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.CodingActionSet" name="Auto::org.eclipse.jdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_6M2foGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.SearchActionSet" name="Auto::org.eclipse.jdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_6M2fomBSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.ui.actionSet.jpaElementCreation" name="Auto::org.eclipse.jpt.jpa.ui.actionSet.jpaElementCreation"/>
  <rootContext xmi:id="_6M2fpGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jst.j2ee.J2eeMainActionSet" name="Auto::org.eclipse.jst.j2ee.J2eeMainActionSet"/>
  <rootContext xmi:id="_6M2fpmBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_6M2fqGBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.java.actionSet" name="Auto::org.eclipse.mylyn.java.actionSet"/>
  <rootContext xmi:id="_6M3GsGBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.java.actionSet.browsing" name="Auto::org.eclipse.mylyn.java.actionSet.browsing"/>
  <rootContext xmi:id="_6M3GsmBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.doc.actionSet" name="Auto::org.eclipse.mylyn.doc.actionSet"/>
  <rootContext xmi:id="_6M3GtGBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_6M3GtmBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_6M3twGBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.SearchActionSet" name="Auto::org.eclipse.pde.ui.SearchActionSet"/>
  <rootContext xmi:id="_6M3twmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_6M3txGBSEfCWTN1FKOuw5w" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_6M3txmBSEfCWTN1FKOuw5w" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_6M4U0GBSEfCWTN1FKOuw5w" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_6M4U0mBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_6M4U1GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_6M4U1mBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_6M4U2GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_6M474GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_6M474mBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_6M475GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_6M5i8GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_6M5i8mBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_6M5i9GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_6M5i9mBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.server.ui.new.actionSet" name="Auto::org.eclipse.wst.server.ui.new.actionSet"/>
  <rootContext xmi:id="_6M6KAGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.server.ui.internal.webbrowser.actionSet" name="Auto::org.eclipse.wst.server.ui.internal.webbrowser.actionSet"/>
  <rootContext xmi:id="_6M6KAmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.web.ui.wizardsActionSet" name="Auto::org.eclipse.wst.web.ui.wizardsActionSet"/>
  <rootContext xmi:id="_6M6KBGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.ws.explorer.explorer" name="Auto::org.eclipse.wst.ws.explorer.explorer"/>
  <descriptors xmi:id="_54mt8GBSEfCWTN1FKOuw5w" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_6G3MYGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ant.ui.views.AntView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" category="Ant" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ant.internal.ui.views.AntView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ant.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Ant</tags>
  </descriptors>
  <descriptors xmi:id="_6G4agGBSEfCWTN1FKOuw5w" elementId="org.eclipse.buildship.ui.views.taskview" label="Gradle Tasks" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/tasks_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.task.TaskView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_6G5BkGBSEfCWTN1FKOuw5w" elementId="org.eclipse.buildship.ui.views.executionview" label="Gradle Executions" iconURI="platform:/plugin/org.eclipse.buildship.ui/icons/full/eview16/executions_view.png" tooltip="" category="Gradle" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.buildship.ui.internal.view.execution.ExecutionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.buildship.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Gradle</tags>
  </descriptors>
  <descriptors xmi:id="_6G6PsGBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.connectivity.DataSourceExplorerNavigator" label="Data Source Explorer" iconURI="platform:/plugin/org.eclipse.datatools.connectivity.ui.dse/icons/full/cview16/enterprise_explorer.gif" tooltip="" category="Data Management" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.datatools.connectivity.ui.dse.views.DataSourceExplorerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.datatools.connectivity.ui.dse"/>
    <tags>View</tags>
    <tags>categoryTag:&#x6570;&#x636e;&#x7ba1;&#x7406;</tags>
    <tags>categoryTag:Data Management</tags>
  </descriptors>
  <descriptors xmi:id="_6G7d0GBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.plan.planView" label="Execution Plan" iconURI="platform:/plugin/org.eclipse.datatools.sqltools.plan/icons/sqlplan.gif" tooltip="" category="Data Management" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.datatools.sqltools.plan.internal.ui.view.PlanView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.datatools.sqltools.plan"/>
    <tags>View</tags>
    <tags>categoryTag:&#x6570;&#x636e;&#x7ba1;&#x7406;</tags>
    <tags>categoryTag:Data Management</tags>
  </descriptors>
  <descriptors xmi:id="_6G8E4GBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.result.resultView" label="SQL Results" iconURI="platform:/plugin/org.eclipse.datatools.sqltools.result.ui/icons/sqlresult.gif" tooltip="" category="Data Management" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.datatools.sqltools.result.internal.ui.view.ResultsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.datatools.sqltools.result.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x6570;&#x636e;&#x7ba1;&#x7406;</tags>
    <tags>categoryTag:Data Management</tags>
  </descriptors>
  <descriptors xmi:id="_6G8r8GBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x8c03;&#x8bd5;</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_6G9TAGBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x8c03;&#x8bd5;</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_6G-hIGBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x8c03;&#x8bd5;</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_6G-hIWBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x8c03;&#x8bd5;</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_6G_IMGBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x8c03;&#x8bd5;</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_6G_vQGBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x8c03;&#x8bd5;</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_6G_vQWBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x8c03;&#x8bd5;</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_6HAWUGBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:&#x8c03;&#x8bd5;</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_6HA9YGBSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.CoverageView" label="Coverage" iconURI="platform:/plugin/org.eclipse.eclemma.ui/icons/full/eview16/coverage.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.eclemma.internal.ui.coverageview.CoverageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.eclemma.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_6HBkcGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_6HCLgGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_6HCLgWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_6HCykGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_6HCykWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_6HDZoGBSEfCWTN1FKOuw5w" elementId="org.eclipse.gef.ui.palette_view" label="Palette" iconURI="platform:/plugin/org.eclipse.gef/icons/palette_view.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.gef.ui.views.palette.PaletteView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.gef"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_6HEAsGBSEfCWTN1FKOuw5w" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5e2e;&#x52a9;</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_6HEnwGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.bcoview.views.BytecodeOutlineView" label="Bytecode" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/bytecodeview.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeOutlineView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_6HEnwWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.bcoview.views.BytecodeReferenceView" label="Bytecode Reference" iconURI="platform:/plugin/org.eclipse.jdt.bcoview/icons/reference.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.bcoview.views.BytecodeReferenceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.bcoview"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_6HFO0GBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.debug.ui.DisplayView" label="Debug Shell" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.debug.ui.display.DisplayView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x8c03;&#x8bd5;</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_6HF14GBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.junit.ResultView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.junit.ui.TestRunnerViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.junit"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_6HGc8GBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.PackageExplorer" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_6HHrEGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.TypeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.typehierarchy.TypeHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_6HISIGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.ProjectsView" label="Projects" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/projects.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.ProjectsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java &#x6d4f;&#x89c8;</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_6HISIWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.PackagesView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.PackagesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java &#x6d4f;&#x89c8;</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_6HI5MGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.TypesView" label="Types" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/types.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.TypesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java &#x6d4f;&#x89c8;</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_6HI5MWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.MembersView" label="Members" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.browsing.MembersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java &#x6d4f;&#x89c8;</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_6HJgQGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" allowMultiple="true" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_6HJgQWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_6HJgQmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.SourceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_6HKHUGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.JavadocView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jdt.internal.ui.infoviews.JavadocView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_6HKHUWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.ui.jpaStructureView" label="JPA Structure" iconURI="platform:/plugin/org.eclipse.jpt.jpa.ui/images/views/jpa-structure.gif" tooltip="" category="JPA" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jpt.jpa.ui.internal.views.JpaStructureView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jpt.jpa.ui"/>
    <tags>View</tags>
    <tags>categoryTag:JPA</tags>
  </descriptors>
  <descriptors xmi:id="_6HLVcGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.ui.jpaDetailsView" label="JPA Details" iconURI="platform:/plugin/org.eclipse.jpt.jpa.ui/images/views/jpa-details.gif" tooltip="" category="JPA" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jpt.jpa.ui.internal.views.JpaDetailsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jpt.jpa.ui"/>
    <tags>View</tags>
    <tags>categoryTag:JPA</tags>
  </descriptors>
  <descriptors xmi:id="_6HLVcWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jst.jsf.ui.component.ComponentTreeView" label="JSF Component Tree" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="JavaServer Faces" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jst.jsf.ui.internal.component.ComponentTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jst.jsf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:JavaServer Faces</tags>
  </descriptors>
  <descriptors xmi:id="_6HL8gGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jst.jsf.ui.tagregistry.TagRegistryView" label="Tag Registry" iconURI="platform:/plugin/org.eclipse.jst.jsf.ui/icons/obj16/library_obj.gif" tooltip="" category="JavaServer Faces" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jst.jsf.ui.internal.tagregistry.TagRegistryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jst.jsf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:JavaServer Faces</tags>
  </descriptors>
  <descriptors xmi:id="_6HMjkGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jst.ws.jaxws.ui.views.AnnotationsView" label="Annotation Properties" iconURI="platform:/plugin/org.eclipse.jst.ws.jaxws.ui/icons/eview16/prop_ps.gif" tooltip="" category="Web Services" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.jst.ws.internal.jaxws.ui.views.AnnotationsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.jst.ws.jaxws.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Web Service</tags>
    <tags>categoryTag:Web Services</tags>
  </descriptors>
  <descriptors xmi:id="_6HMjkWBSEfCWTN1FKOuw5w" elementId="org.eclipse.lsp4e.callHierarchy.callHierarchyView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/call_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.callhierarchy.CallHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_6HNKoGBSEfCWTN1FKOuw5w" elementId="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/type_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_6HNKoWBSEfCWTN1FKOuw5w" elementId="org.eclipse.lsp4e.ui.languageServersView" label="Language Servers" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.ui.LanguageServersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_6HNxsGBSEfCWTN1FKOuw5w" elementId="org.eclipse.m2e.core.views.MavenRepositoryView" label="Maven Repositories" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/maven_indexes.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenRepositoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_6HNxsWBSEfCWTN1FKOuw5w" elementId="org.eclipse.m2e.core.views.MavenLifecycleMappingsView" label="Maven Lifecycle Mappings" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/main_tab.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.MavenLifecycleMappingsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_6HOYwGBSEfCWTN1FKOuw5w" elementId="org.eclipse.m2e.core.views.MavenBuild" label="Maven Workspace Build" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.m2e.core.ui.internal.views.build.BuildDebugView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.m2e.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_6HOYwWBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.builds.navigator.builds" label="Builds" iconURI="platform:/plugin/org.eclipse.mylyn.builds.ui/icons/eview16/build-view.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.builds.ui.view.BuildsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.builds.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_6HO_0GBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.commons.identity.ui.navigator.People" label="People" iconURI="platform:/plugin/org.eclipse.mylyn.commons.identity.ui/icons/obj16/people.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.identity.ui.PeopleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.identity.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_6HPm4GBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.repositories.ui.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.repositories.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_6HPm4WBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.reviews.Explorer" label="Review" iconURI="platform:/plugin/org.eclipse.mylyn.reviews.ui/icons/obj16/review.png" tooltip="View artifacts and comments associated with reviews." category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.reviews.ui.views.ReviewExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.reviews.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_6HQN8GBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_6HQN8WBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskRepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_6HQ1AGBSEfCWTN1FKOuw5w" elementId="org.eclipse.oomph.p2.ui.RepositoryExplorer" label="Repository Explorer" iconURI="platform:/plugin/org.eclipse.oomph.p2.ui/icons/obj16/repository.gif" tooltip="" category="Oomph" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.oomph.p2.internal.ui.RepositoryExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.oomph.p2.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Oomph</tags>
  </descriptors>
  <descriptors xmi:id="_6HQ1AWBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.api.tools.ui.views.apitooling.views.apitoolingview" label="API Tools" iconURI="platform:/plugin/org.eclipse.pde.api.tools.ui/icons/full/obj16/api_tools.png" tooltip="" category="API Tools" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.api.tools.ui.internal.views.APIToolingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.api.tools.ui"/>
    <tags>View</tags>
    <tags>categoryTag:API &#x5de5;&#x5177;</tags>
    <tags>categoryTag:API Tools</tags>
  </descriptors>
  <descriptors xmi:id="_6HRcEGBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.runtime.RegistryBrowser" label="Plug-in Registry" iconURI="platform:/plugin/org.eclipse.pde.runtime/icons/eview16/registry.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.runtime.registry.RegistryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.runtime"/>
    <tags>View</tags>
    <tags>categoryTag:&#x63d2;&#x4ef6;&#x5f00;&#x53d1;</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_6HSDIGBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.PluginsView" label="Plug-ins" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/eview16/plugin_depend.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.plugins.PluginsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x63d2;&#x4ef6;&#x5f00;&#x53d1;</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_6HSqMGBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.FeaturesView" label="Features" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/feature_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.features.FeaturesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x63d2;&#x4ef6;&#x5f00;&#x53d1;</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_6HTRQGBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.DependenciesView" label="Plug-in Dependencies" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/req_plugins_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.dependencies.DependenciesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x63d2;&#x4ef6;&#x5f00;&#x53d1;</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_6HTRQWBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.TargetPlatformState" label="Target Platform State" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/target_profile_xml_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.target.TargetStateView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x63d2;&#x4ef6;&#x5f00;&#x53d1;</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_6HT4UGBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.ImageBrowserView" label="Plug-in Image Browser" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/psearch_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.pde.internal.ui.views.imagebrowser.ImageBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.pde.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x63d2;&#x4ef6;&#x5f00;&#x53d1;</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_6HT4UWBSEfCWTN1FKOuw5w" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_6HVGcGBSEfCWTN1FKOuw5w" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_6HVtgGBSEfCWTN1FKOuw5w" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_6HVtgWBSEfCWTN1FKOuw5w" elementId="org.eclipse.tips.ide.tipPart" label="Tip of the Day" iconURI="platform:/plugin/org.eclipse.tips.ui/icons/lightbulb.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.tips.ide/org.eclipse.tips.ide.internal.TipPart">
    <tags>View</tags>
    <tags>categoryTag:&#x5e2e;&#x52a9;</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_6HVtgmBSEfCWTN1FKOuw5w" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x7ec8;&#x7aef;</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_6HWUkGBSEfCWTN1FKOuw5w" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.OldTerminalsViewHandler"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5176;&#x4ed6;</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_6HWUkWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_6HW7oGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_6HXisGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5e2e;&#x52a9;</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_6HXisWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_6HYJwGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_6HYw0GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_6HYw0WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_6HZX4GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_6HZX4WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_6HZ-8GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_6HZ-8WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_6HamAGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_6HbNEGBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_6Hb0IGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_6Hb0IWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" label="Snippets" iconURI="platform:/plugin/org.eclipse.wst.common.snippets/icons/snippets_view.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.common.snippets.internal.ui.SnippetsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.common.snippets"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5e38;&#x89c4;</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_6HcbMGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.internet.monitor.view" label="TCP/IP Monitor" iconURI="platform:/plugin/org.eclipse.wst.internet.monitor.ui/icons/cview16/monitorView.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.internet.monitor.ui.internal.view.MonitorView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.internet.monitor.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x8c03;&#x8bd5;</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_6HdCQGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/call_hierarchy.gif" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.jsdt.internal.ui.callhierarchy.CallHierarchyViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.jsdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5176;&#x4ed6;</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_6HeQYGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/source.gif" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.jsdt.internal.ui.infoviews.SourceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.jsdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5176;&#x4ed6;</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_6HeQYWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.JavadocView" label="Documentation" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/javadoc.gif" tooltip="JavaScript Documentation" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.jsdt.internal.ui.infoviews.JavadocView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.jsdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x5176;&#x4ed6;</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_6He3cGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.server.ui.ServersView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" category="Server" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.server.ui.internal.cnf.ServersView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.server.ui"/>
    <tags>View</tags>
    <tags>categoryTag:&#x670d;&#x52a1;&#x5668;</tags>
    <tags>categoryTag:Server</tags>
  </descriptors>
  <descriptors xmi:id="_6HfegGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xml.ui.views.annotations.XMLAnnotationsView" label="Documentation" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/obj16/comment_obj.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xml.ui.internal.views.annotations.XMLAnnotationsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_6HfegWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xml.ui.contentmodel.view" label="Content Model" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/view16/hierarchy.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xml.ui.internal.views.contentmodel.ContentModelView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_6HgFkGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xml.views.XPathView" label="XPath" iconURI="platform:/plugin/org.eclipse.wst.xml.xpath.ui/icons/full/xpath.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xml.xpath.ui.internal.views.XPathView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xml.xpath.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_6HgsoGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xsl.jaxp.debug.ui.resultview" label="Result" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xsl.jaxp.debug.ui.internal.views.ResultView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xsl.jaxp.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_6HgsoWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xsl.ui.view.outline" label="Stylesheet Model" iconURI="platform:/plugin/org.eclipse.wst.xsl.ui/icons/full/hierarchy.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xsl.ui.internal.views.stylesheet.StylesheetModelView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xsl.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <trimContributions xmi:id="_2r10UF9tEeO-yojH_y4TJA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_76uUAF9tEeO-yojH_y4TJA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8tJPcF9tEeO-yojH_y4TJA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_9LgmcF9tEeO-yojH_y4TJA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_51WloGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51XMsGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51XMsWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51XMsmBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_51UwiWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51XMs2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_51XMtGBSEfCWTN1FKOuw5w" elementId="org.eclipse.oomph.p2.ui.SearchRequirements" commandName="Search Requirements" category="_51UwgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51XzwGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to field" description="Invokes quick assist and selects 'Convert local variable to field'" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51XzwWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51XzwmBSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.junitPluginShortcut.coverage" commandName="Coverage JUnit Plug-in Test" description="Coverage JUnit Plug-in Test" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xzw2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.FetchGitLabMergeRequest" commandName="Fetch GitLab Merge Request" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51XzxGBSEfCWTN1FKOuw5w" elementId="org.eclipse.lsp4e.openTypeHierarchy" commandName="Open Type Hierarchy" description="Open Type Hierarchy for the selected item" category="_51UwlWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51XzxWBSEfCWTN1FKOuw5w" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_51VXgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51XzxmBSEfCWTN1FKOuw5w" elementId="org.eclipse.oomph.setup.editor.openDiscoveredType" commandName="Open Discovered Type" category="_51UwkWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xzx2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51XzyGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51XzyWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51XzymBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_51Uwe2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xzy2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51XzzGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51XzzWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51XzzmBSEfCWTN1FKOuw5w" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_51UwiWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51Xzz2BSEfCWTN1FKOuw5w" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_51Xz0GBSEfCWTN1FKOuw5w" elementId="org.eclipse.emf.codegen.ui.jet.goto.matching.bracket" commandName="Goto Matching Bracket" description="Goto Matching Bracket" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz0WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ant.ui.toggleMarkOccurrences" commandName="Toggle Ant Mark Occurrences" description="Toggles mark occurrences in Ant editors" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz0mBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz02BSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.connectivity.commands.export" commandName="Export Profiles Command" description="Command to export connection profiles" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz1GBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz1WBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.junit.junitShortcut.debug" commandName="Debug JUnit Test" description="Debug JUnit Test" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz1mBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz12BSEfCWTN1FKOuw5w" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_51VXhGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz2GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ant.ui.open.declaration.command" commandName="Open Declaration" description="Opens the Ant editor on the referenced element" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz2WBSEfCWTN1FKOuw5w" elementId="org.eclipse.tm4e.languageconfiguration.toggleLineCommentCommand" commandName="Toggle Line Comment" category="_51UwjGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz2mBSEfCWTN1FKOuw5w" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz22BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Methods" description="Add delegate methods for a type's fields" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz3GBSEfCWTN1FKOuw5w" elementId="org.eclipse.gef.ui.palette_view" commandName="Palette" category="_51Uwi2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz3WBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xml.views.XPathView.prefixes" commandName="&amp;Edit Namespace Prefixes" category="_51UwhGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz3mBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.sqleditor.toggleCommentAction" commandName="Toggle Comment" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz32BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_51UwdWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz4GBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz4WBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz4mBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz42BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.searchForTask" commandName="Search Repository for Task" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz5GBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz5WBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz5mBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz52BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.commit.UnifiedDiffCommand" commandName="Show Unified Diff" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz6GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_51Uwe2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz6WBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz6mBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_51UwjmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz62BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz7GBSEfCWTN1FKOuw5w" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_51Uwl2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz7WBSEfCWTN1FKOuw5w" elementId="org.eclipse.oomph.setup.editor.importProjects" commandName="Import Projects" category="_51UwkWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz7mBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz72BSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.hideUnusedElements" commandName="Hide Unused Elements" category="_51UwemBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz8GBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Fields" description="Choose fields to initialize and constructor from superclass to call " category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz8WBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xml.ui.disable.grammar.constraints" commandName="Turn off Grammar Constraints" description="Turn off grammar Constraints" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Xz8mBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.commands.showElementInTypeHierarchyView" commandName="Show Java Element Type Hierarchy" description="Show a Java element in the Type Hierarchy view" category="_51Uwk2BSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51Xz82BSEfCWTN1FKOuw5w" elementId="elementRef" name="Java &#x5143;&#x7d20;&#x5f15;&#x7528;" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_51Xz9GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya0GBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya0WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya0mBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya02BSEfCWTN1FKOuw5w" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_51UweGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya1GBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya1WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_51UwnGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya1mBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_51UwnGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya12BSEfCWTN1FKOuw5w" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_51Uwl2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya2GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya2WBSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.localJavaShortcut.coverage" commandName="Coverage Java Application" description="Coverage Java Application" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya2mBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya22BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya3GBSEfCWTN1FKOuw5w" elementId="org.eclipse.oomph.setup.editor.refreshCache" commandName="Refresh Remote Cache" category="_51UwkWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya3WBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify var access" description="Invokes quick assist and selects 'Qualify var access'" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya3mBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.run" commandName="Run Java Applet" description="Run Java Applet" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya32BSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.exportSession" commandName="Export Session..." category="_51UwemBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya4GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya4WBSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.scalaShortcut.coverage" commandName="Coverage Scala Application" description="Coverage Scala Application" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya4mBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya42BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_51Uwe2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya5GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_51UwnGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya5WBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya5mBSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.dumpExecutionData" commandName="Dump Execution Data" category="_51UwemBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya52BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.debug.ui.commands.ToggleLambdaEntryBreakpoint" commandName="Toggle Lambda Entry Breakpoint" description="Creates or removes a lambda entry breakpoint" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya6GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya6WBSEfCWTN1FKOuw5w" elementId="org.eclipse.buildship.ui.commands.runtasks" commandName="Run Gradle Tasks" description="Runs all the selected Gradle tasks" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya6mBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.toggleBreadcrumb" commandName="Toggle Java Editor Breadcrumb" description="Toggle the Java editor breadcrumb" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya62BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya7GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ant.ui.renameInFile" commandName="Rename In File" description="Renames all references within the same buildfile" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya7WBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya7mBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya72BSEfCWTN1FKOuw5w" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_51VXhWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya8GBSEfCWTN1FKOuw5w" elementId="org.eclipse.m2e.actions.LifeCycleGenerateSources.run" commandName="Run Maven Generate Sources" description="Run Maven Generate Sources" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya8WBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya8mBSEfCWTN1FKOuw5w" elementId="org.eclipse.jst.ws.jaxws.ui.configure.handlers" commandName="Configure Handlers" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya82BSEfCWTN1FKOuw5w" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya9GBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.sse.ui.structure.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya9WBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.debug.ui.commands.AddExceptionBreakpoint" commandName="Add Java Exception Breakpoint" description="Add a Java exception breakpoint" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya9mBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya92BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya-GBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.debug.ui.commands.ToggleTracepoint" commandName="Toggle Tracepoint" description="Creates or removes a tracepoint" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Ya-WBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_51UwiWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51Ya-mBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_51ZB4GBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB4WBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_51Uwd2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB4mBSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.junitShortcut.coverage" commandName="Coverage JUnit Test" description="Coverage JUnit Test" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB42BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_51UwdWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB5GBSEfCWTN1FKOuw5w" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Local Terminal on Selection" category="_51UwlmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB5WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB5mBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB52BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or method" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB6GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB6WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB6mBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB62BSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.workbenchShortcut.coverage" commandName="Coverage Eclipse Application" description="Coverage Eclipse Application" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB7GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_51Uwe2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB7WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB7mBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB72BSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.ui.newEntity" commandName="JPA Entity" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB8GBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB8WBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB8mBSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.commands.OpenCoverageConfiguration" commandName="Coverage Configurations..." description="Coverage Configurations..." category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB82BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.return.continue.targets" commandName="Search break/continue Target Occurrences in File" description="Search for break/continue target occurrences of a selected target name" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB9GBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB9WBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter methods for type's fields" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB9mBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.edit.text.format" commandName="Format Source" description="Format a PDE Source Page" category="_51Uwh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB92BSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.result.terminate" commandName="Terminate Result" category="_51UwfmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB-GBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB-WBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.builds.ui.command.ShowBuildOutput" commandName="Show Build Output" category="_51Uwc2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB-mBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesViewCollapseWorkingTree" commandName="Collapse Working Tree" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB-2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB_GBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB_WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB_mBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZB_2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.open.implementation" commandName="Open Implementation" description="Opens the Implementations of a method or a type in its hierarchy" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZCAGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.junit.gotoTest" commandName="Referring Tests" description="Referring Tests" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZCAWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZCAmBSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.openSessionExecutionData" commandName="Open Execution Data" category="_51UwemBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZCA2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.commands.showElementInPackageView" commandName="Show Java Element in Package Explorer" description="Select Java element in the Package Explorer view" category="_51Uwk2BSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51ZCBGBSEfCWTN1FKOuw5w" elementId="elementRef" name="Java &#x5143;&#x7d20;&#x5f15;&#x7528;" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_51ZCBWBSEfCWTN1FKOuw5w" elementId="org.eclipse.oomph.setup.editor.performDropdown" commandName="Perform Dropdown" category="_51UwkWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZCBmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.debug.ui.commands.InstanceCount" commandName="Instance Count" description="View the instance count of the selected type loaded in the target VM" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZCB2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZCCGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZCCWBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.addAllPluginsToJavaSearch" commandName="Add All Plug-ins to Java Workspace Scope" description="Adds all plug-ins in the target platform to Java workspace scope" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZCCmBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_51UwiWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51ZCC2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="&#x9879;&#x76ee;" optional="false"/>
  </commands>
  <commands xmi:id="_51ZCDGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZCDWBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.quickOutline" commandName="Quick Outline" description="Open a quick outline popup dialog for a given editor input" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZCDmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZCD2BSEfCWTN1FKOuw5w" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZCEGBSEfCWTN1FKOuw5w" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_51UwimBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZCEWBSEfCWTN1FKOuw5w" elementId="org.eclipse.m2e.profiles.ui.commands.selectMavenProfileCommand" commandName="Select Maven Profiles" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZCEmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZCE2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZCFGBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_51Uwd2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZCFWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_51Uwm2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZCFmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZCF2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Zo8GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Zo8WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Zo8mBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Zo82BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Zo9GBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Zo9WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Zo9mBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Zo92BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Zo-GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Zo-WBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Zo-mBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.java.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_51UwlGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Zo-2BSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.runtime.spy.commands.spyCommand" commandName="Plug-in Selection Spy" description="Show the Plug-in Spy" category="_51VXiGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Zo_GBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Zo_WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Zo_mBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51Zo_2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpAGBSEfCWTN1FKOuw5w" elementId="org.eclipse.lsp4e.symbolinworkspace" commandName="Go to Symbol in Workspace" category="_51UwlWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpAWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_51Uwe2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpAmBSEfCWTN1FKOuw5w" elementId="org.eclipse.oomph.ui.ToggleOfflineMode" commandName="Toggle Offline Mode" category="_51UwiGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpA2BSEfCWTN1FKOuw5w" elementId="org.eclipse.oomph.setup.editor.openLog" commandName="Open Setup Log" category="_51UwkWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpBGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_51UwnGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpBWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_51Uwe2BSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51ZpBmBSEfCWTN1FKOuw5w" elementId="importWizardId" name="&#x5bfc;&#x5165;&#x5411;&#x5bfc;"/>
  </commands>
  <commands xmi:id="_51ZpB2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpCGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpCWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpCmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_51Uwm2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpC2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpDGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into Java comments" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpDWBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpDmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpD2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the JavaScript file" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpEGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpEWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpEmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpE2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpFGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_51UwnGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpFWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpFmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpF2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpGGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.JavaBrowsingPerspective" commandName="Java Browsing" description="Show the Java Browsing perspective" category="_51VXhGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpGWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpGmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpG2BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElement" commandName="Open Build Element" category="_51Uwc2BSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51ZpHGBSEfCWTN1FKOuw5w" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_51ZpHWBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.team.ui.commands.CopyCommitMessage" commandName="Copy Commit Message for Task" description="Copies a commit message for the currently selected task to the clipboard." category="_51Uwd2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpHmBSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.relaunchSession" commandName="Relaunch Coverage Session" category="_51UwemBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpH2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpIGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpIWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" description="Check out, rename, create, or delete a branch in a git repository" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpImBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xml.ui.previousSibling" commandName="Previous Sibling" description="Go to Previous Sibling" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpI2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpJGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpJWBSEfCWTN1FKOuw5w" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_51UwiWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51ZpJmBSEfCWTN1FKOuw5w" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_51ZpJ2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ant.ui.antShortcut.run" commandName="Run Ant Build" description="Run Ant Build" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpKGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpKWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpKmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.debug.ui.commands.Inspect" commandName="Inspect" description="Inspect result of evaluating selected text" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpK2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpLGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpLWBSEfCWTN1FKOuw5w" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpLmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ZpL2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQAGBSEfCWTN1FKOuw5w" elementId="org.eclipse.oomph.setup.donate" commandName="Sponsor" description="Sponsor to the development of the Eclipse IDE" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQAWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to var" description="Invokes quick assist and selects 'Convert local variable to var'" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQAmBSEfCWTN1FKOuw5w" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQA2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQBGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQBWBSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.junitRAPShortcut.coverage" commandName="Coverage RAP JUnit Test" description="Coverage RAP JUnit Test" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQBmBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.ReplaceWithTheirs" commandName="Replace Conflicting Files with Their Revision" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQB2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQCGBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_51Uwd2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQCWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.debug.ui.commands.Watch" commandName="Watch" description="Create new watch expression" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQCmBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQC2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQDGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.server.stop" commandName="Stop" description="Stop the server" category="_51UweWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQDWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_51Uwm2BSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51aQDmBSEfCWTN1FKOuw5w" elementId="href" name="&#x5e2e;&#x52a9;&#x4e3b;&#x9898;&#x8d85;&#x94fe;&#x63a5;"/>
  </commands>
  <commands xmi:id="_51aQD2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQEGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQEWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQEmBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.sqleditor.saveAsTemplateAction" commandName="Save as Template" category="_51VXg2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQE2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQFGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQFWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQFmBSEfCWTN1FKOuw5w" elementId="org.eclipse.oomph.p2.ui.ExploreRepository" commandName="Explore Repository" category="_51UwgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQF2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQGGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="Open Refactoring History " description="Opens the refactoring history" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQGWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQGmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQG2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.log.jdt.showinconsole" commandName="&amp;Show In Console" description="&#x5728;&#x63a7;&#x5236;&#x53f0;&#x4e2d;&#x663e;&#x793a;&#x5806;&#x6808;&#x8ddf;&#x8e2a;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQHGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQHWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQHmBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQH2BSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQIGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQIWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_51UwiWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51aQImBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_51aQI2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQJGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQJWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQJmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQJ2BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_51UwjmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQKGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQKWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQKmBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQK2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQLGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQLWBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.sqleditor.refreshFromDatabaseAction" commandName="Refresh from Database" category="_51VXg2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQLmBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteAsOneStatementAction" commandName="Execute Selected Text As One Statement" category="_51VXg2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQL2BSEfCWTN1FKOuw5w" elementId="org.eclipse.lsp4e.formatfile" commandName="Format" category="_51UwlWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQMGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQMWBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.connectivity.commands.addrepository" commandName="New Repository Profile Command" description="Command to create a new repository profile" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQMmBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQM2BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.builds.ui.command.NewTaskFromTest" commandName="New Task From Test" category="_51Uwc2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQNGBSEfCWTN1FKOuw5w" elementId="org.eclipse.oomph.setup.editor.perform.startup" commandName="Perform Setup Tasks (Startup)" category="_51UwkWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQNWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQNmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jst.jsp.ui.add.imports" commandName="Add Im&amp;port" description="Create import declaration for selection" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQN2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.eclipselink.ui.upgradeToEclipseLinkMappingFile" commandName="Upgrade to EclipseLink Mapping File" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51aQOGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_51UwfWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51aQOWBSEfCWTN1FKOuw5w" elementId="url" name="Url&#x5730;&#x5740;"/>
    <parameters xmi:id="_51aQOmBSEfCWTN1FKOuw5w" elementId="browserId" name="&#x6d4f;&#x89c8;&#x5668;&#x6807;&#x8bc6;"/>
    <parameters xmi:id="_51aQO2BSEfCWTN1FKOuw5w" elementId="name" name="&#x6d4f;&#x89c8;&#x5668;&#x540d;"/>
    <parameters xmi:id="_51aQPGBSEfCWTN1FKOuw5w" elementId="tooltip" name="&#x6d4f;&#x89c8;&#x5668;&#x5de5;&#x5177;&#x63d0;&#x793a;"/>
  </commands>
  <commands xmi:id="_51aQPWBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.result.removeAllInstances" commandName="Remove All Visible Results" category="_51UwfmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3EGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3EWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3EmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3E2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_51Uwe2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3FGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3FWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3FmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_51UwfWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51a3F2BSEfCWTN1FKOuw5w" elementId="Splitter.isHorizontal" name="&#x65b9;&#x4f4d;" optional="false"/>
  </commands>
  <commands xmi:id="_51a3GGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3GWBSEfCWTN1FKOuw5w" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_51Uwl2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3GmBSEfCWTN1FKOuw5w" elementId="org.eclipse.lsp4e.togglelinkwitheditor" commandName="Toggle Link with Editor" category="_51UwlWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3G2BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3HGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3HWBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3HmBSEfCWTN1FKOuw5w" elementId="org.eclipse.lsp4e.format" commandName="Format" category="_51UwlWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3H2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3IGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3IWBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.createAntBuildFile" commandName="Create Ant Build File" description="Creates an Ant build file for the current project" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3ImBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_51Uwd2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3I2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3JGBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaeditor.copycolumn" commandName="Copy" category="_51UwmGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3JWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Toggle &quot;Link with Editor and Selection&quot; (Git Repositories View)" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3JmBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_51UwdWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3J2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3KGBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3KWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.validation.ValidationCommand" commandName="Validate" description="Invoke registered Validators" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3KmBSEfCWTN1FKOuw5w" elementId="org.eclipse.tm4e.languageconfiguration.addBlockCommentCommand" commandName="Add Block Comment" category="_51UwjGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3K2BSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.sqleditor.debugAction" commandName="Debug" category="_51VXg2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3LGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.add.javadoc.comment" commandName="Add Javadoc Comment" description="Add a Javadoc comment stub to the member element" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3LWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Interactive Rebase" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3LmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3L2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3MGBSEfCWTN1FKOuw5w" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_51UweGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3MWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jaxb.ui.generateJaxbClasses" commandName="JAXB Classes..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3MmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3M2BSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.openDependencies" commandName="Open Plug-in Dependencies" description="Opens the plug-in dependencies view for the current plug-in" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3NGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in Java editors" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3NWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3NmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected method" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3N2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3OGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3OWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3OmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_51Uwe2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3O2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3PGBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3PWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_51Uwe2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3PmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3P2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_51Uwe2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3QGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a Java editor" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3QWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3QmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected method" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3Q2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51a3RGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beIGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beIWBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_51UwjmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beImBSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.testNgSuiteShortcut.coverage" commandName="Coverage TestNG Suite" description="Coverage TestNG Suite" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beI2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beJGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xml.ui.reload.dependencies" commandName="Reload Dependencies" description="Reload Dependencies" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beJWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beJmBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.internal.merge.ToggleCurrentChangesCommand" commandName="Ignore Changes from Ancestor to Current Version" description="Toggle ignoring changes only between the ancestor and the current version in a three-way merge comparison" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beJ2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beKGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.junit.junitShortcut.rerunFailedFirst" commandName="Rerun JUnit Test - Failures First" description="Rerun JUnit Test - Failures First" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beKWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_51Uwe2BSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51beKmBSEfCWTN1FKOuw5w" elementId="exportWizardId" name="&#x5bfc;&#x51fa;&#x5411;&#x5bfc;"/>
  </commands>
  <commands xmi:id="_51beK2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xsd.ui.refactor.makeTypeGlobal" commandName="Make &amp;Anonymous Type Global" description="&#x5c06;&#x533f;&#x540d;&#x7c7b;&#x578b;&#x63d0;&#x5347;&#x4e3a;&#x5168;&#x5c40;&#x7ea7;&#x522b;&#x5e76;&#x66ff;&#x6362;&#x5b83;&#x7684;&#x5f15;&#x7528;" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beLGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beLWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beLmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.JavaPerspective" commandName="Java" description="&#x663e;&#x793a; Java &#x900f;&#x89c6;&#x56fe;" category="_51VXhGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beL2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.indent" commandName="Correct Indentation" description="Corrects the indentation of the selected lines" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beMGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beMWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beMmBSEfCWTN1FKOuw5w" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beM2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_51UwnGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beNGBSEfCWTN1FKOuw5w" elementId="org.eclipse.gef.zoom_in" commandName="Zoom In" description="Zoom In" category="_51VXhmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beNWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.with.resources" commandName="Surround with try-with-resources Block" description="Surround the selected text with a try-with-resources block" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beNmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beN2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_51Uwe2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beOGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beOWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beOmBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.externalizeStrings" commandName="Externalize Strings in Plug-ins" description="Extract translatable strings from plug-in files" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beO2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_51Uwe2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51bePGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.ReplaceWithOurs" commandName="Replace Conflicting Files with Our Revision" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51bePWBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.connectivity.commands.showcategory" commandName="Show Category" description="Show Category" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51bePmBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beP2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.extractMethodInplace.assist" commandName="Quick Assist - Extract method" description="Invokes quick assist and selects 'Extract to method'" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beQGBSEfCWTN1FKOuw5w" elementId="org.eclipse.oomph.p2.ui.SearchRepositories" commandName="Search Repositories" category="_51UwgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beQWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beQmBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beQ2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beRGBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beRWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beRmBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beR2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in JavaScript editors" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beSGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beSWBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.github.ui.command.createGist" commandName="Create Gist" description="Create Gist based on selection" category="_51UwiWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51beSmBSEfCWTN1FKOuw5w" elementId="publicGist" name="Public Gist"/>
  </commands>
  <commands xmi:id="_51beS2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.ui.persistentAttributeMapAs" commandName="Map As" category="_51UwhWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51beTGBSEfCWTN1FKOuw5w" elementId="specifiedPersistentAttributeMappingKey" name="specified mapping key" optional="false"/>
    <parameters xmi:id="_51beTWBSEfCWTN1FKOuw5w" elementId="defaultPersistentAttributeMappingKey" name="default mapping key" optional="false"/>
  </commands>
  <commands xmi:id="_51beTmBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.index.ui.command.ResetIndex" commandName="Refresh Search Index" category="_51Uwd2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beT2BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.github.ui.command.rebasePullRequest" commandName="Rebase pull request" description="Rebase onto destination branch" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beUGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beUWBSEfCWTN1FKOuw5w" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_51VXgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beUmBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beU2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beVGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to var" description="Invokes quick assist and selects 'Assign to var'" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51beVWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.ui.convertJavaGenerators" commandName="Move Java Generators to XML..." category="_51Uwg2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFMGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_51Uwk2BSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51cFMWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="&#x8d44;&#x6e90;&#x7cfb;&#x7edf;&#x8def;&#x5f84;&#x53c2;&#x6570;"/>
  </commands>
  <commands xmi:id="_51cFMmBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_51Uwd2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFM2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFNGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFNWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFNmBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.connectivity.commands.addprofile" commandName="New Connection Profile Command" description="Command to create a new connection profile" category="_51UwiWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51cFN2BSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.connectivity.ui.ignoreCategory" name="ignoreCategory"/>
    <parameters xmi:id="_51cFOGBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.connectivity.ui.useSelection" name="useSelection"/>
  </commands>
  <commands xmi:id="_51cFOWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFOmBSEfCWTN1FKOuw5w" elementId="org.eclipse.m2e.discovery.ui" commandName="m2e Marketplace" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFO2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFPGBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_51Uwd2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFPWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_51Uwe2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFPmBSEfCWTN1FKOuw5w" elementId="org.eclipse.codegen.ui.jet.rename" commandName="Rename" description="Rename" category="_51UwkGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFP2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.assignAllParamsToNewFields.assist" commandName="Quick Assist - Assign all parameters to new fields" description="Invokes quick assist and selects 'Assign all parameters to new fields'" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFQGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFQWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFQmBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_51Uwd2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFQ2BSEfCWTN1FKOuw5w" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_51UwimBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFRGBSEfCWTN1FKOuw5w" elementId="org.eclipse.buildship.ui.commands.rundefaulttasks" commandName="Run Gradle Default Tasks" description="Runs the default tasks of the selected Gradle project" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFRWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.ui.generateEntities" commandName="Generate Entities from Tables..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFRmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFR2BSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.removeActiveSession" commandName="Remove Active Session" category="_51UwemBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFSGBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_51Uwd2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFSWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFSmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFS2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.indent" commandName="Indent Line" description="Indents the current line" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFTGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFTWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jst.pagedesigner.vertical" commandName="Vertical Layout" category="_51UwjWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFTmBSEfCWTN1FKOuw5w" elementId="org.eclipse.codegen.ui.jet.source.quickmenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_51UwkmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFT2BSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFUGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.ui.addToPersistenceUnit" commandName="Add to Persistence Unit" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFUWBSEfCWTN1FKOuw5w" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_51Uwl2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFUmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_51Uwe2BSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51cFU2BSEfCWTN1FKOuw5w" elementId="mayPrompt" name="&#x53ef;&#x80fd;&#x63d0;&#x793a;"/>
  </commands>
  <commands xmi:id="_51cFVGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFVWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFVmBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFV2BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFWGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFWWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFWmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.codemining" commandName="Toggle Code Mining" description="Toggle Code Mining Annotations" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFW2BSEfCWTN1FKOuw5w" elementId="org.eclipse.m2e.core.ui.command.updateProject" commandName="Update Maven Project" description="Update Maven project configuration and dependencies" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFXGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFXWBSEfCWTN1FKOuw5w" elementId="refresh.schema.editor.action.id" commandName="Refresh from Server" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFXmBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFX2BSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.organizeManifest" commandName="Organize Manifests" description="Cleans up plug-in manifest files" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFYGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFYWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.junit.junitShortcut.run" commandName="Run JUnit Test" description="Run JUnit Test" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFYmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.ui.convertJavaQueries" commandName="Move Java Queries to XML..." category="_51Uwg2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFY2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFZGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFZWBSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.swtBotJunitShortcut.coverage" commandName="Coverage SWTBot Test" description="Coverage SWTBot Test" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cFZmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csQGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jst.jsp.ui.refactor.move" commandName="Move" description="Move a Java Element to another package" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csQWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the compilation unit" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csQmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_51UwnGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csQ2BSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaeditor.pastecolumn" commandName="Paste" category="_51UwmGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csRGBSEfCWTN1FKOuw5w" elementId="org.eclipse.m2e.core.pomFileAction.run" commandName="Run Maven Build" description="Run Maven Build" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csRWBSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.linkWithSelection" commandName="Link with Current Selection" category="_51UwemBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csRmBSEfCWTN1FKOuw5w" elementId="org.eclipse.m2e.actions.LifeCycleInstall.run" commandName="Run Maven Install" description="Run Maven Install" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csR2BSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteCurrentAction" commandName="Execute Current Text" category="_51VXg2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csSGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csSWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csSmBSEfCWTN1FKOuw5w" elementId="org.eclipse.oomph.setup.editor.perform" commandName="Perform Setup Tasks" category="_51UwkWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csS2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.raw.paste" commandName="Raw Paste" description="Paste and ignore smart insert setting" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csTGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_51Uwm2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csTWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csTmBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csT2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csUGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csUWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csUmBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_51UwhmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csU2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.commit.DiffEditorQuickOutlineCommand" commandName="Quick Outline" description="Show the quick outline for a unified diff" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csVGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csVWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ant.ui.antShortcut.debug" commandName="Debug Ant Build" description="Debug Ant Build" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csVmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csV2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csWGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_51UwfWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51csWWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="&#x900f;&#x89c6;&#x56fe;&#x6807;&#x8bc6;"/>
  </commands>
  <commands xmi:id="_51csWmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csW2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xml.ui.nextSibling" commandName="Next Sibling" description="Go to Next Sibling" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csXGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a JavaScript editor" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csXWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_51Uwm2BSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51csXmBSEfCWTN1FKOuw5w" elementId="cheatSheetId" name="&#x6807;&#x8bc6;" optional="false"/>
    <parameters xmi:id="_51csX2BSEfCWTN1FKOuw5w" elementId="name" name="&#x540d;&#x79f0;" optional="false"/>
    <parameters xmi:id="_51csYGBSEfCWTN1FKOuw5w" elementId="url" name="Url&#x5730;&#x5740;" optional="false"/>
  </commands>
  <commands xmi:id="_51csYWBSEfCWTN1FKOuw5w" elementId="revert.schema.editor.action.id" commandName="Revert Object" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csYmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xsl.debug.ui.launchshortcut.debug" commandName="Debug XSLT Transformation" description="Create a configuration to debug an XSLT transformation" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csY2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csZGBSEfCWTN1FKOuw5w" elementId="org.eclipse.m2e.core.ui.command.addDependency" commandName="Add Maven Dependency" description="Add Maven dependency" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csZWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.debug.ui.commands.Execute" commandName="Execute" description="Evaluate selected text" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csZmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csZ2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.core.synchronizeClasses" commandName="Synchronize Class List" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csaGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.navigate.gotopackage" commandName="Go to Folder" description="Go to Folder" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csaWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csamBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_51Uwe2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csa2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.server.launchShortcut.run" commandName="Run on Server" description="Run the current selection on a server" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csbGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csbWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.server.launchShortcut.debug" commandName="Debug on Server" description="Debug the current selection on a server" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csbmBSEfCWTN1FKOuw5w" elementId="org.eclipse.m2e.editor.RenameArtifactAction" commandName="Rename Maven Artifact..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csb2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cscGBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteSQLAction" commandName="Execute All" category="_51VXg2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cscWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51cscmBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.api.tools.ui.remove.filters" commandName="Remove API Problem Filters..." description="Remove API problem filters for this project" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csc2BSEfCWTN1FKOuw5w" elementId="org.eclipse.tm.terminal.view.ui.command.newview" commandName="New Terminal View" category="_51UwlmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csdGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csdWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51csdmBSEfCWTN1FKOuw5w" elementId="org.eclipse.lsp4e.showkindinoutline" commandName="Show Kind in Outline" category="_51UwlWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTUGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_51Uwe2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTUWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTUmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the compilation unit" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTU2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_51UwiWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51dTVGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_51dTVWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTVmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() methods for the type" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTV2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_51Uwk2BSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51dTWGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.showIn.targetId" name="&#x663e;&#x793a;&#x5230;&#x76ee;&#x6807;id" optional="false"/>
  </commands>
  <commands xmi:id="_51dTWWBSEfCWTN1FKOuw5w" elementId="sed.tabletree.collapseAll" commandName="Collapse All" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTWmBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTW2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_51UwgWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51dTXGBSEfCWTN1FKOuw5w" elementId="title" name="&#x6807;&#x9898;"/>
    <parameters xmi:id="_51dTXWBSEfCWTN1FKOuw5w" elementId="message" name="&#x6d88;&#x606f;"/>
    <parameters xmi:id="_51dTXmBSEfCWTN1FKOuw5w" elementId="initialValue" name="&#x521d;&#x59cb;&#x503c;"/>
    <parameters xmi:id="_51dTX2BSEfCWTN1FKOuw5w" elementId="cancelReturns" name="&#x53d6;&#x6d88;&#x65f6;&#x7684;&#x8fd4;&#x56de;&#x503c;"/>
  </commands>
  <commands xmi:id="_51dTYGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTYWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTYmBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_51Uwd2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTY2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTZGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTZWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTZmBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTZ2BSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.commands.CoverageLast" commandName="Coverage" description="Coverage" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTaGBSEfCWTN1FKOuw5w" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_51Uwl2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTaWBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaeditor.cutcolumn" commandName="Cut" category="_51UwmGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTamBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTa2BSEfCWTN1FKOuw5w" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTbGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTbWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository..." description="Adds an existing Git repository to the Git Repositories view" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTbmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.debug.ui.breakpoint.properties" commandName="Java Breakpoint Properties" description="View and edit the properties for a given Java breakpoint" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTb2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTcGBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_51Uwd2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTcWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTcmBSEfCWTN1FKOuw5w" elementId="org.eclipse.buildship.ui.commands.refreshproject" commandName="Refresh Gradle Project" description="Synchronizes the Gradle builds of the selected projects with the workspace" category="_51UwcmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTc2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTdGBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_51UwdWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTdWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xml.views.XPathView.processor.xpathprocessor" commandName="XPath Processor" category="_51UwhGBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51dTdmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.commands.radioStateParameter" name="State" optional="false"/>
  </commands>
  <commands xmi:id="_51dTd2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTeGBSEfCWTN1FKOuw5w" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_51UwlmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTeWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_51UwfWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51dTemBSEfCWTN1FKOuw5w" elementId="preferencePageId" name="&#x9996;&#x9009;&#x9879;&#x9875;&#x9762;"/>
  </commands>
  <commands xmi:id="_51dTe2BSEfCWTN1FKOuw5w" elementId="org.eclipse.m2e.sourcelookup.ui.openSourceLookupInfoDialog" commandName="Source Lookup Info" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTfGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTfWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTfmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTf2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTgGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTgWBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTgmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTg2BSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.debug" commandName="Debug OSGi Framework" description="Debug OSGi Framework" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dThGBSEfCWTN1FKOuw5w" elementId="org.eclipse.lsp4e.symbolinfile" commandName="Go to Symbol in File" category="_51UwlWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dThWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dThmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTh2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTiGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTiWBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTimBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTi2BSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.resetOnDump" commandName="Reset on Dump" category="_51UwemBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51dTjGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6YGBSEfCWTN1FKOuw5w" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_51VXgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6YWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6YmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.sse.ui.add.block.comment" commandName="Add Block Comment" description="Add Block Comment" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6Y2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6ZGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.debug.ui.commands.AllInstances" commandName="All Instances" description="View all instances of the selected type loaded in the target VM" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6ZWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" description="Opens selected commit(s) in Commit Viewer(s)" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6ZmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.assignInTryWithResources.assist" commandName="Quick Assist - Assign to variable in new try-with-resources block" description="Invokes quick assist and selects 'Assign to variable in new try-with-resources block'" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6Z2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6aGBSEfCWTN1FKOuw5w" elementId="org.eclipse.m2e.actions.LifeCycleTest.run" commandName="Run Maven Test" description="Run Maven Test" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6aWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xsl.debug.ui.launchshortcut.run" commandName="Run XSLT Transformation" description="Create a configuration to debug an XSLT transformation" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6amBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6a2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to field" description="Invokes quick assist and selects 'Assign parameter to field'" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6bGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6bWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.move.inner.to.top.level" commandName="Move Type to New File" description="Move Type to New File" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6bmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6b2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.sse.ui.open.file.from.source" commandName="Open Selection" description="&#x5bf9;&#x6240;&#x9009;&#x94fe;&#x63a5;&#x6253;&#x5f00;&#x7f16;&#x8f91;&#x5668;" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6cGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6cWBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.builds.ui.command.ShowTestResults" commandName="Show Test Results" category="_51Uwc2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6cmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6c2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.sse.ui.goto.matching.bracket" commandName="Matching Character" description="Go to Matching Character" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6dGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.FetchGiteaPullRequest" commandName="Fetch Gitea Pull Request" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6dWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6dmBSEfCWTN1FKOuw5w" elementId="org.eclipse.lsp4e.selectionRange.up" commandName="Enclosing Element" description="Expand Selection To Enclosing Element" category="_51UwlWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6d2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6eGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.override.methods" commandName="Override/Implement Methods" description="Override or implement methods from super types" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6eWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6emBSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jaxb.eclipselink.ui.command.addEclipseLinkJaxbProperty" commandName="Add EclipseLink JAXB property" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6e2BSEfCWTN1FKOuw5w" elementId="org.eclipse.lsp4e.selectionRange.down" commandName="Restore To Last Selection" description="Expand Selection To Restore To Last Selection" category="_51UwlWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6fGBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6fWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6fmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6f2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6gGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository..." description="Clones a Git repository and adds the clone to the Git Repositories view" category="_51VXgmBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51d6gWBSEfCWTN1FKOuw5w" elementId="repositoryUri" name="Repository URI"/>
  </commands>
  <commands xmi:id="_51d6gmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6g2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.sse.ui.quick_outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6hGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.add.textblock" commandName="Add Text Block" description="Adds Text Block" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6hWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6hmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ant.ui.openExternalDoc" commandName="Open External Documentation" description="Open the External documentation for the current task in the Ant editor" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6h2BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.github.ui.command.mergePullRequest" commandName="Merge pull request" description="Merge into destination branch" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6iGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Git Repository..." description="Creates a new Git repository and adds it to the Git Repositories view" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6iWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6imBSEfCWTN1FKOuw5w" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_51UwfGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6i2BSEfCWTN1FKOuw5w" elementId="org.eclipse.tm4e.languageconfiguration.removeBlockCommentCommand" commandName="Remove Block Comment" category="_51UwjGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6jGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6jWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6jmBSEfCWTN1FKOuw5w" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_51Uwm2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6j2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6kGBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6kWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6kmBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_51UwdWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6k2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_51Uwe2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6lGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6lWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6lmBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_51UwdWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6l2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6mGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6mWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6mmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6m2BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6nGBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6nWBSEfCWTN1FKOuw5w" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_51UwiWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51d6nmBSEfCWTN1FKOuw5w" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_51d6n2BSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6oGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6oWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6omBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6o2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6pGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6pWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51d6pmBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_51UwdWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehcGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.javadoc.comment" commandName="Add JSDoc Comment" description="Add a JSDoc comment stub to the member element" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehcWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehcmBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.api.tools.ui.convert.javadocs" commandName="Convert API Tools Javadoc Tags..." description="Starts a wizard that will allow you to convert existing Javadoc tags to annotations" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehc2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehdGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.open.external.javadoc" commandName="Open Attached Javadoc" description="Open the attached Javadoc of the selected element in a browser" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehdWBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.run" commandName="Run OSGi Framework" description="Run OSGi Framework" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehdmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehd2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.sse.ui.structure.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51eheGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51eheWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.debug.ui.commands.AddClassPrepareBreakpoint" commandName="Add Class Load Breakpoint" description="Add a class load breakpoint" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehemBSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.ui.convertJavaProjectToJpa" commandName="Convert to JPA Project..." category="_51Uwe2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehe2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesCreateGroup" commandName="Create a Repository Group" description="Create a repository group for structuring repositories in the Git Repositories view" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehfGBSEfCWTN1FKOuw5w" elementId="org.eclipse.emf.codegen.ecore.ui.Generate" commandName="Generate Code" description="Generate code for the EMF models in the workspace" category="_51UwmmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehfWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.generate.javadoc" commandName="Generate JSDoc" description="Generates JSDoc for a selectable set of JavaScript resources" category="_51UwnGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehfmBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehf2BSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_51VXhGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehgGBSEfCWTN1FKOuw5w" elementId="org.eclipse.tips.ide.command.open" commandName="Tip of the Day" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehgWBSEfCWTN1FKOuw5w" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_51UweGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehgmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_51UwcGBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51ehg2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.specific_content_assist.category_id" name="&#x7c7b;&#x578b;" optional="false"/>
  </commands>
  <commands xmi:id="_51ehhGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehhWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehhmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_51Uwe2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehh2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.method.exits" commandName="Search Method Exit Occurrences in File" description="Search for method exit occurrences of a selected return type" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehiGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehiWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehimBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_51Uwe2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehi2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehjGBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehjWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.eclipselink.ui.generateDynamicEntities" commandName="Generate Dynamic Entities from Tables..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehjmBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehj2BSEfCWTN1FKOuw5w" elementId="org.eclipse.m2e.sourcelookup.ui.importBinaryProject" commandName="Import Binary Project" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehkGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehkWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehkmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Field" description="Convert a local variable to a field" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehk2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehlGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehlWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehlmBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehl2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_51UwnGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehmGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Field" description="Create getting and setting methods for the field and use only those to access the field" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehmWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehmmBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.run" commandName="Run Eclipse Application" description="Run Eclipse Application" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehm2BSEfCWTN1FKOuw5w" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehnGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.sse.ui.remove.block.comment" commandName="Remove Block Comment" description="Remove Block Comment" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehnWBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.runtime.spy.commands.menuSpyCommand" commandName="Plug-in Menu Spy" description="Show the Plug-in Spy" category="_51VXiGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehnmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.ui.entityMappingsAddPersistentClass" commandName="Add Class..." category="_51UwhWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehn2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Vars" description="Choose vars to initialize and constructor from superclass to call " category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehoGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehoWBSEfCWTN1FKOuw5w" elementId="org.eclipse.m2e.core.ui.command.openPom" commandName="Open Maven POM" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehomBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.index.rebuild" commandName="Rebuild Java Index" description="Rebuilds the Java index database" category="_51UwnGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51eho2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehpGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xsd.ui.refactor.rename.element" commandName="&amp;Rename XSD element" description="&#x91cd;&#x547d;&#x540d; XSD &#x5143;&#x7d20;" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehpWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehpmBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51ehp2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIgGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_51Uwi2BSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51fIgWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.showView.viewId" name="&#x89c6;&#x56fe;"/>
    <parameters xmi:id="_51fIgmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.showView.secondaryId" name="&#x8f85;&#x52a9; Id"/>
    <parameters xmi:id="_51fIg2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.views.showView.makeFast" name="&#x4f5c;&#x4e3a;&#x5feb;&#x901f;&#x89c6;&#x56fe;"/>
  </commands>
  <commands xmi:id="_51fIhGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIhWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIhmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_51Uwk2BSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51fIh2BSEfCWTN1FKOuw5w" elementId="resourcePath" name="&#x8d44;&#x6e90;&#x8def;&#x5f84;" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_51fIiGBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIiWBSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.selectRootElements" commandName="Select Root Elements" category="_51UwemBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51fIimBSEfCWTN1FKOuw5w" elementId="type" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_51fIi2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_51Uwe2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIjGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIjWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIjmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIj2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIkGBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.openPluginArtifact" commandName="Open Plug-in Artifact" description="Open a plug-in artifact in the manifest editor" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIkWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.server.debug" commandName="Debug" description="Debug server" category="_51UweWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIkmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIk2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new method parameter based on the selected expression" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIlGBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_51Uwd2BSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51fIlWBSEfCWTN1FKOuw5w" elementId="connectorKind" name="&#x4ed3;&#x5e93;&#x7c7b;&#x578b;"/>
  </commands>
  <commands xmi:id="_51fIlmBSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.removeAllSessions" commandName="Remove All Sessions" category="_51UwemBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIl2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.run" commandName="Run Java Application" description="Run Java Application" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fImGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fImWBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElementWithBrowser" commandName="Open Build with Browser" category="_51Uwc2BSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51fImmBSEfCWTN1FKOuw5w" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_51fIm2BSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.updateUnitVersions" commandName="Update IU Versions from Repositories" description="Update to latest IU versions" category="_51Uwh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fInGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.sse.ui.structure.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fInWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.wsdl.ui.refactor.rename.element" commandName="Rename WSDL component" description="Renames WSDL component" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fInmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_51Uwm2BSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51fIn2BSEfCWTN1FKOuw5w" elementId="cheatSheetId" name="&#x6807;&#x8bc6;"/>
  </commands>
  <commands xmi:id="_51fIoGBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_51Uwd2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIoWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIomBSEfCWTN1FKOuw5w" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIo2BSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.searchTargetRepositories" commandName="Add Artifact to Target Platform" description="Add an artifact to your target platform" category="_51UwiWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51fIpGBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.searchTargetRepositories.term" name="The initial search pattern for the artifact search dialog"/>
  </commands>
  <commands xmi:id="_51fIpWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIpmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIp2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIqGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_51UwnGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIqWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIqmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIq2BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.github.ui.command.checkoutPullRequest" commandName="Checkout Pull Request" description="Checkout pull request into topic branch" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIrGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIrWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.server.publish" commandName="Publish" description="Publish to server" category="_51UweWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIrmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory method to encapsulate invocation of the selected constructor" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIr2BSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.updateClasspath" commandName="Update Classpath" description="Updates the plug-in classpath from latest settings" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIsGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIsWBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_51Uwd2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIsmBSEfCWTN1FKOuw5w" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIs2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.sse.ui.structure.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fItGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fItWBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.builds.ui.command.NewTaskFromBuild" commandName="New Task From Build" category="_51Uwc2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fItmBSEfCWTN1FKOuw5w" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_51UwlmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIt2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIuGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xsd.ui.refactor.makeElementGlobal" commandName="Make Local Element &amp;Global" description="&#x5c06;&#x672c;&#x5730;&#x5143;&#x7d20;&#x63d0;&#x5347;&#x4e3a;&#x5168;&#x5c40;&#x7ea7;&#x522b;&#x5e76;&#x66ff;&#x6362;&#x5b83;&#x7684;&#x5f15;&#x7528;" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIuWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.encapsulateField.assist" commandName="Quick Assist - Create getter/setter for field" description="Invokes quick assist and selects 'Create getter/setter for field'" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIumBSEfCWTN1FKOuw5w" elementId="org.eclipse.oomph.setup.editor.openEditorDropdown" commandName="Open Setup Editor" category="_51UwkWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIu2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_51Uwm2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIvGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIvWBSEfCWTN1FKOuw5w" elementId="org.eclipse.m2e.sourcelookup.ui.openPom" commandName="Open Pom" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIvmBSEfCWTN1FKOuw5w" elementId="org.eclipse.oomph.setup.ui.questionnaire" commandName="Configuration Questionnaire" description="Review the IDE's most fiercely contested preferences" category="_51UwkWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIv2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIwGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIwWBSEfCWTN1FKOuw5w" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_51Uwm2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIwmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIw2BSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.sqleditor.GotoMatchingTokenAction" commandName="Goto Matching Token" category="_51VXg2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIxGBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.api.tools.ui.setup.projects" commandName="API Tools Setup..." description="Configure projects for API usage and compatibility checks" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIxWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.debug" commandName="Debug Java Application" description="Debug Java Application" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fIxmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvkGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvkWBSEfCWTN1FKOuw5w" elementId="org.eclipse.buildship.ui.commands.openbuildscript" commandName="Open Gradle Build Script" description="Opens the Gradle build script for the selected Gradle project" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvkmBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.FetchGitHubPR" commandName="Fetch GitHub Pull Request" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvk2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.eclipselink.ui.convertJavaConverters" commandName="Move Java Converters to XML..." category="_51Uwg2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvlGBSEfCWTN1FKOuw5w" elementId="org.eclipse.buildship.ui.commands.openrunconfiguration" commandName="Open Gradle Run Configuration" description="Opens the Run Configuration for the selected Gradle tasks" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvlWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvlmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvl2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvmGBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.github.ui.command.fetchPullRequest" commandName="Fetch Pull Request Commits" description="Fetch commits from pull request" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvmWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvmmBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvm2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvnGBSEfCWTN1FKOuw5w" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_51VXgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvnWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvnmBSEfCWTN1FKOuw5w" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_51Uwl2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvn2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xml.ui.generate.xml" commandName="XML File..." description="Generate a XML file from the selected DTD or Schema" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvoGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvoWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to var" description="Invokes quick assist and selects 'Assign parameter to var'" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvomBSEfCWTN1FKOuw5w" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_51VXhWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51fvo2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_51fvpGBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.importFromRepository" commandName="Import Plug-in from a Repository" description="Imports a plug-in from a source repository" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvpWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvpmBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvp2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_51Uwe2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvqGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvqWBSEfCWTN1FKOuw5w" elementId="org.eclipse.emf.codegen.ui.jet.select.enclosing" commandName="Select Enclosing JET Element" description="Select Enclosing JET Element" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvqmBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvq2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvrGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvrWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvrmBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_51Uwd2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvr2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvsGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static field and uses the new static field" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvsWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvsmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.sse.ui.cleanup.document" commandName="Cleanup Document..." description="Cleanup document" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvs2BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.builds.ui.command.ShowBuildOutput.url" commandName="Show Build Output" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvtGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvtWBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvtmBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvt2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvuGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify field access" description="Invokes quick assist and selects 'Qualify field access'" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvuWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvumBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.sse.ui.format" commandName="Format" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvu2BSEfCWTN1FKOuw5w" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvvGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvvWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvvmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvv2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvwGBSEfCWTN1FKOuw5w" elementId="org.eclipse.m2e.core.ui.command.addPlugin" commandName="Add Maven Plugin" description="Add Maven plugin" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvwWBSEfCWTN1FKOuw5w" elementId="sed.tabletree.expandAll" commandName="Expand All" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvwmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.modify.method.parameters" commandName="Change Method Signature" description="Change method signature includes parameter names and parameter order" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvw2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvxGBSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.mergeSessions" commandName="Merge Sessions" category="_51UwemBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvxWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable (replace all occurrences)" description="Invokes quick assist and selects 'Extract local variable (replace all occurrences)'" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvxmBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvx2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvyGBSEfCWTN1FKOuw5w" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_51Uwm2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvyWBSEfCWTN1FKOuw5w" elementId="org.eclipse.lsp4e.openCallHierarchy" commandName="Open Call Hierarchy" description="Open Call Hierarchy for the selected item" category="_51UwlWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvymBSEfCWTN1FKOuw5w" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_51Uwl2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvy2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.external.javadoc" commandName="Open External JSDoc" description="Open the JSDoc of the selected element in an external browser" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvzGBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvzWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvzmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fvz2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.eclipselink.ui.persistentTypeAddVirtualAttribute" commandName="Add Virtual Attribute..." category="_51UwhWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv0GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_51VXhGBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51fv0WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="&#x53c2;&#x6570;"/>
    <parameters xmi:id="_51fv0mBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="&#x5728;&#x65b0;&#x7a97;&#x53e3;"/>
  </commands>
  <commands xmi:id="_51fv02BSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.eclipselink.ui.newDynamicEntity" commandName="EclipseLink Dynamic Entity" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv1GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv1WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv1mBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv12BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.debug" commandName="Debug Java Applet" description="Debug Java Applet" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv2GBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xml.ui.gotoMatchingTag" commandName="Matching Tag" description="Go to Matching Tag" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv2WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv2mBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.sqleditor.saveToDatabaseAction" commandName="Save to Database" category="_51VXg2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv22BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv3GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv3WBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv3mBSEfCWTN1FKOuw5w" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_51Uwl2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv32BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_51UwnGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv4GBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.history.ShowBlame" commandName="Show Revision Information" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv4WBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv4mBSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.ui.persistentAttributeAddToXml" commandName="Add Attribute to XML" category="_51UwhWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv42BSEfCWTN1FKOuw5w" elementId="org.eclipse.tips.ide.command.trim.open" commandName="Tip of the Day" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv5GBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv5WBSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.selectCounters" commandName="Select Counters" category="_51UwemBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51fv5mBSEfCWTN1FKOuw5w" elementId="type" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_51fv52BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_51UwnGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv6GBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv6WBSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.ui.persistentTypeMapAs" commandName="Map As" category="_51UwhWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51fv6mBSEfCWTN1FKOuw5w" elementId="persistentTypeMappingKey" name="&#x6620;&#x5c04;&#x952e;" optional="false"/>
  </commands>
  <commands xmi:id="_51fv62BSEfCWTN1FKOuw5w" elementId="org.eclipse.epp.package.common.contribute" commandName="Contribute" description="Contribute to the development and success of the Eclipse IDE!" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv7GBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.sqleditor.DMLDialogSelectionAction" commandName="Edit in SQL Query Builder..." category="_51VXg2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv7WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv7mBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.debug" commandName="Debug JUnit Plug-in Test" description="Debug JUnit Plug-in Test" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv72BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.github.ui.command.cloneGist" commandName="Clone Gist" description="Clone Gist into Git repository" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv8GBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.extractLocalNotReplaceOccurrences.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv8WBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.sqleditor.attachProfileAction" commandName="Set Connection Information" category="_51VXg2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv8mBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51fv82BSEfCWTN1FKOuw5w" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_51VXhWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWoGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_51Uwe2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWoWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.debug.ui.commands.AllReferences" commandName="All References" description="Inspect all references to the selected object" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWomBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.junit.junitShortcut.rerunLast" commandName="Rerun JUnit Test" description="Rerun JUnit Test" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWo2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWpGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.sse.ui.format.active.elements" commandName="Format Active Elements" description="Format active elements" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWpWBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.connectivity.commands.import" commandName="Import Profiles Command" description="Command to import connection profiles" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWpmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWp2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWqGBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_51Uwd2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWqWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWqmBSEfCWTN1FKOuw5w" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWq2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWrGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWrWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_51Uwm2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWrmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWr2BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.builds.ui.command.ShowTestResults.url" commandName="Show Test Results" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWsGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWsWBSEfCWTN1FKOuw5w" elementId="org.eclipse.m2e.actions.LifeCycleClean.run" commandName="Run Maven Clean" description="Run Maven Clean" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWsmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWs2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWtGBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWtWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWtmBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWt2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.debug.ui.commands.ForceReturn" commandName="Force Return" description="Forces return from method with value of selected expression" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWuGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWuWBSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.importSession" commandName="Import Session..." category="_51UwemBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWumBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWu2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWvGBSEfCWTN1FKOuw5w" elementId="org.eclipse.lsp4e.toggleSortOutline" commandName="Sort" category="_51UwlWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWvWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_51UwgWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51gWvmBSEfCWTN1FKOuw5w" elementId="title" name="&#x6807;&#x9898;"/>
    <parameters xmi:id="_51gWv2BSEfCWTN1FKOuw5w" elementId="message" name="&#x6d88;&#x606f;"/>
    <parameters xmi:id="_51gWwGBSEfCWTN1FKOuw5w" elementId="imageType" name="&#x56fe;&#x50cf;&#x7c7b;&#x578b;&#x5e38;&#x91cf;" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_51gWwWBSEfCWTN1FKOuw5w" elementId="defaultIndex" name="&#x7f3a;&#x7701;&#x6309;&#x94ae;&#x7d22;&#x5f15;" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_51gWwmBSEfCWTN1FKOuw5w" elementId="buttonLabel0" name="&#x7b2c;&#x4e00;&#x4e2a;&#x6309;&#x94ae;&#x6807;&#x6ce8;"/>
    <parameters xmi:id="_51gWw2BSEfCWTN1FKOuw5w" elementId="buttonLabel1" name="&#x7b2c;&#x4e8c;&#x4e2a;&#x6309;&#x94ae;&#x6807;&#x6ce8;"/>
    <parameters xmi:id="_51gWxGBSEfCWTN1FKOuw5w" elementId="buttonLabel2" name="&#x7b2c;&#x4e09;&#x4e2a;&#x6309;&#x94ae;&#x6807;&#x6ce8;"/>
    <parameters xmi:id="_51gWxWBSEfCWTN1FKOuw5w" elementId="buttonLabel3" name="&#x7b2c;&#x56db;&#x4e2a;&#x6309;&#x94ae;&#x6807;&#x6ce8;"/>
    <parameters xmi:id="_51gWxmBSEfCWTN1FKOuw5w" elementId="cancelReturns" name="&#x53d6;&#x6d88;&#x65f6;&#x7684;&#x8fd4;&#x56de;&#x503c;"/>
  </commands>
  <commands xmi:id="_51gWx2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWyGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWyWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.ui.generateDDL" commandName="Generate Tables from Entities..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWymBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xml.ui.referencedFileErrors" commandName="Show Details..." description="Show Details..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWy2BSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.sqleditor.runAction" commandName="Run" category="_51VXg2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWzGBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWzWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWzmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.debug.ui.command.OpenFromClipboard" commandName="Open from Clipboard" description="Opens a Java element or a Java stack trace from clipboard" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gWz2BSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.internationalize" commandName="Internationalize Plug-ins" description="Sets up internationalization for a plug-in" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW0GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW0WBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW0mBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW02BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW1GBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW1WBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW1mBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.JavaHierarchyPerspective" commandName="Java Type Hierarchy" description="Show the Java Type Hierarchy perspective" category="_51VXhGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW12BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW2GBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.multicatch" commandName="Surround with try/multi-catch Block" description="Surround the selected text with a try/multi-catch block" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW2WBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW2mBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW22BSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW3GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_51UwiWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51gW3WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&#x5206;&#x5c42;&#x7684;(&amp;H)"/>
    <parameters xmi:id="_51gW3mBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.commands.radioStateParameter" name="&#x5d4c;&#x5957;&#x9879;&#x76ee;&#x89c6;&#x56fe;&#x2014;&#x5355;&#x9009;&#x72b6;&#x6001;" optional="false"/>
  </commands>
  <commands xmi:id="_51gW32BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW4GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW4WBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter.object" commandName="Introduce Parameter Object" description="Introduce a parameter object to a selected method" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW4mBSEfCWTN1FKOuw5w" elementId="org.eclipse.gef.zoom_out" commandName="Zoom Out" description="Zoom Out" category="_51VXhmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW42BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.commands.openElementInEditor" commandName="Open Java Element" description="Open a Java element in its editor" category="_51Uwk2BSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51gW5GBSEfCWTN1FKOuw5w" elementId="elementRef" name="Java &#x5143;&#x7d20;&#x5f15;&#x7528;" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_51gW5WBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected JavaScript comment lines" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW5mBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.result.removeInstance" commandName="Remove Result" category="_51UwfmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW52BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW6GBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW6WBSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.ui.persistentAttributeAddToXmlAndMap" commandName="Add Attribute to XML and Map..." category="_51UwhWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW6mBSEfCWTN1FKOuw5w" elementId="org.eclipse.buildship.ui.commands.refreshtaskview" commandName="Refresh View (Gradle Tasks)" description="Refreshes the Gradle Tasks view" category="_51Uwi2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW62BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.sse.ui.toggle.comment" commandName="Toggle Comment" description="Toggle Comment" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW7GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_51Uwm2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW7WBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW7mBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW72BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW8GBSEfCWTN1FKOuw5w" elementId="org.eclipse.oomph.setup.editor.synchronizePreferences" commandName="Synchronize Preferences" category="_51UwkWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW8WBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xml.ui.cmnd.contentmodel.sych" commandName="Synch" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW8mBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW82BSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW9GBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW9WBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xsd.ui.refactor.renameTargetNamespace" commandName="Rename Target Namespace" description="Changes the target namespace of the schema" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW9mBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.builds.ui.command.AbortBuild" commandName="Abort Build" category="_51Uwc2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW92BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW-GBSEfCWTN1FKOuw5w" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_51UwimBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW-WBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.generate.javadoc" commandName="Generate Javadoc" description="Generates Javadoc for a selectable set of Java resources" category="_51UwnGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW-mBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW-2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.debug.ui.commands.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW_GBSEfCWTN1FKOuw5w" elementId="org.eclipse.buildship.ui.shortcut.test.run" commandName="Run Gradle Test" description="Run Gradle test based on the current selection" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW_WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW_mBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gW_2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jst.pagedesigner.design" commandName="Graphical Designer" category="_51UwjWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gXAGBSEfCWTN1FKOuw5w" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_51VXgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gXAWBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51gXAmBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9sGBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.openManifest" commandName="Open Manifest" description="Open the plug-in manifest" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9sWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9smBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.generate.tostring" commandName="Generate toString()" description="Generates the toString() method for the type" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9s2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9tGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9tWBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9tmBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.api.tools.ui.compare.to.baseline" commandName="API Baseline..." description="Allows to compare the selected resource with the current baseline" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9t2BSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.imagebrowser.saveToWorkspace" commandName="Save Image" description="Save the selected image into a project in the workspace" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9uGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.debug.ui.commands.Display" commandName="Display" description="Display result of evaluating selected text" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9uWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9umBSEfCWTN1FKOuw5w" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_51UwlmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9u2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9vGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to field" description="Invokes quick assist and selects 'Assign to field'" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9vWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9vmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.extract.method" commandName="Extract Method" description="Extract a set of statements or an expression into a new method and use the new method" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9v2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.eclipselink.ui.newEclipseLinkMappingFile" commandName="EclipseLink ORM Mapping File" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9wGBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9wWBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9wmBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.CompareWithEachOther" commandName="Compare with Each Other" description="Compare two files selected in the Compare Editor with each other." category="_51Uwl2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9w2BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_51UwdWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9xGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jst.jsp.ui.refactor.rename" commandName="Rename" description="Rename a Java Element" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9xWBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9xmBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.run" commandName="Run JUnit Plug-in Test" description="Run JUnit Plug-in Test" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9x2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.navigate.gotopackage" commandName="Go to Package" description="Go to Package" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9yGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9yWBSEfCWTN1FKOuw5w" elementId="org.eclipse.codegen.ui.jet.format" commandName="Format" description="Format" category="_51UwkmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9ymBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_51Uwd2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9y2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9zGBSEfCWTN1FKOuw5w" elementId="org.eclipse.buildship.ui.commands.addbuildshipnature" commandName="Add Gradle Nature" description="Adds the Gradle nature and synchronizes this project as if the Gradle Import wizard had been run on its location." category="_51UwcmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9zWBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="&#x53d1;&#x9001;&#x6587;&#x4ef6;&#x7ed3;&#x675f;&#x7b26;" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9zmBSEfCWTN1FKOuw5w" elementId="org.eclipse.jst.pagedesigner.horizotal" commandName="Horizontal Layout" category="_51UwjWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9z2BSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g90GBSEfCWTN1FKOuw5w" elementId="org.eclipse.lsp4e.typeHierarchy" commandName="Quick Type Hierarchy" description="Open Quick Call Hierarchy for the selected item" category="_51UwlWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g90WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g90mBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g902BSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g91GBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g91WBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g91mBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g912BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.bugs.commands.newTaskFromMarker" commandName="New Task from Marker..." description="Report as Bug from Marker" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g92GBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.server.run" commandName="Run" description="Run server" category="_51UweWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g92WBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_51UwiWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51g92mBSEfCWTN1FKOuw5w" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_51g922BSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.testNgShortcut.coverage" commandName="Coverage TestNG Test" description="Coverage TestNG Test" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g93GBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g93WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_51UwfWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51g93mBSEfCWTN1FKOuw5w" elementId="plugin" name="&#x63d2;&#x4ef6;"/>
    <parameters xmi:id="_51g932BSEfCWTN1FKOuw5w" elementId="path" name="&#x8def;&#x5f84;"/>
  </commands>
  <commands xmi:id="_51g94GBSEfCWTN1FKOuw5w" elementId="org.eclipse.jst.pagedesigner.source" commandName="Source Code" category="_51UwjWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g94WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_51Uwm2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g94mBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.common.project.facet.ui.ConvertProjectToFacetedForm" commandName="Convert to Faceted Form..." category="_51Uwe2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g942BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.annotate.classFile" commandName="Annotate Class File" description="Externally add Annotations to a Class File." category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g95GBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g95WBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g95mBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g952BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.sse.ui.format.document" commandName="Format" description="Format selection" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g96GBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g96WBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g96mBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g962BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_51Uwe2BSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51g97GBSEfCWTN1FKOuw5w" elementId="newWizardId" name="&#x201c;&#x65b0;&#x5efa;&#x201d;&#x5411;&#x5bfc;"/>
  </commands>
  <commands xmi:id="_51g97WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g97mBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected Java comment lines" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g972BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.builds.ui.commands.CopyDetails" commandName="Copy Details" category="_51Uwc2BSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51g98GBSEfCWTN1FKOuw5w" elementId="kind" name="Kind"/>
    <parameters xmi:id="_51g98WBSEfCWTN1FKOuw5w" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_51g98mBSEfCWTN1FKOuw5w" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g982BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.builds.ui.command.RunBuild" commandName="Run Build" category="_51Uwc2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g99GBSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui.selectActiveSession" commandName="Select Active Session..." category="_51UwemBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g99WBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with Each Other" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g99mBSEfCWTN1FKOuw5w" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g992BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9-GBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_51UwdWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9-WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9-mBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_51VXgmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9-2BSEfCWTN1FKOuw5w" elementId="org.eclipse.codegen.ui.jet.refactor.quickmenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_51UwkGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9_GBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_51VXgWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9_WBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g9_mBSEfCWTN1FKOuw5w" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_51UwiWBSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51g9_2BSEfCWTN1FKOuw5w" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_51g-AGBSEfCWTN1FKOuw5w" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_51g-AWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.gotoBreadcrumb" commandName="Show In Breadcrumb" description="Shows the Java editor breadcrumb and sets the keyboard focus into it" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g-AmBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.debug" commandName="Debug Eclipse Application" description="Debug Eclipse Application" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g-A2BSEfCWTN1FKOuw5w" elementId="org.eclipse.userstorage.ui.showPullDown" commandName="Show Pull Down Menu" category="_51Uwm2BSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51g-BGBSEfCWTN1FKOuw5w" elementId="intoolbar" name="In Tool Bar" optional="false"/>
  </commands>
  <commands xmi:id="_51g-BWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.ui.makePersistent" commandName="Make Persistent..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g-BmBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteSelectionAction" commandName="Execute Selected Text" category="_51VXg2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g-B2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jaxb.ui.command.createPackageInfo" commandName="Create package-info.java" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g-CGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.edit.text.java.extract.class" commandName="Extract Class..." description="Extracts fields into a new class" category="_51UwmWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g-CWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_51VXh2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g-CmBSEfCWTN1FKOuw5w" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_51Uwl2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g-C2BSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_51UwgGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g-DGBSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.ui.newMappingFile" commandName="JPA ORM Mapping File" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g-DWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g-DmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into JavaScript comments" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g-D2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g-EGBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElementWithBrowser.url" commandName="Open Build with Browser" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g-EWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.ui.xmlFileUpgradeToLatestVersion" commandName="Upgrade JPA Document Version" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g-EmBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_51UwdGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g-E2BSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.sse.ui.search.find.occurrences" commandName="Occurrences in File" description="Find occurrences of the selection in the file" category="_51UwcGBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g-FGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_51UwfWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g-FWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g-FmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.sse.ui.outline.customFilter" commandName="&amp;Filters" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g-F2BSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.sqlscrapbook.commands.openscrapbook" commandName="Open SQL Scrapboo&amp;k" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51g-GGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_51Uwk2BSEfCWTN1FKOuw5w">
    <parameters xmi:id="_51g-GWBSEfCWTN1FKOuw5w" elementId="filePath" name="&#x6587;&#x4ef6;&#x8def;&#x5f84;" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_51g-GmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_51UwdmBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_51hkwGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the JavaScript file" category="_51Uwk2BSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6F6KIGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.ant.ui.actionSet.presentation/org.eclipse.ant.ui.toggleAutoReconcile" commandName="&#x5207;&#x6362; Ant &#x7f16;&#x8f91;&#x5668;&#x81ea;&#x52a8;&#x534f;&#x8c03;" description="&#x5207;&#x6362; Ant &#x7f16;&#x8f91;&#x5668;&#x81ea;&#x52a8;&#x534f;&#x8c03;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6F6KIWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.datatools.sqltools.sqlscrapbook.actionSet/org.eclipse.datatools.sqltools.sqlscrapbook.actions.OpenScrapbookAction" commandName="Open SQL Scrapbook" description="&#x6253;&#x5f00; SQL &#x526a;&#x8d34;&#x7c3f;&#x7f16;&#x8f91; SQL &#x72b6;&#x6001;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6F7YQGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="&#x8fd0;&#x884c;&#x65b9;&#x5f0f;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6F7_UGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="&#x8fd0;&#x884c;&#x5386;&#x53f2;&#x8bb0;&#x5f55;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6F7_UWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="&#x8fd0;&#x884c;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6F7_UmBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="&#x8c03;&#x8bd5;&#x65b9;&#x5f0f;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6F8mYGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="&#x8c03;&#x8bd5;&#x5386;&#x53f2;&#x8bb0;&#x5f55;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6F8mYWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="&#x8c03;&#x8bd5;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6F8mYmBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="&#x914d;&#x7f6e;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6F9NcGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="&#x6982;&#x8981;&#x5206;&#x6790;&#x65b9;&#x5f0f;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6F9NcWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="&#x6982;&#x8981;&#x5206;&#x6790;&#x5386;&#x53f2;&#x8bb0;&#x5f55;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6F9NcmBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.eclemma.ui.CoverageActionSet/org.eclipse.eclemma.ui.actions.CoverageDropDownAction" commandName="Coverage" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6F9Nc2BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.eclemma.ui.CoverageActionSet/org.eclipse.eclemma.ui.actions.CoverageAsAction" commandName="Coverage As" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6F90gGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.eclemma.ui.CoverageActionSet/org.eclipse.eclemma.ui.actions.CoverageHistoryAction" commandName="Coverage History" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6F90gWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GAQwGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.NewTypeDropDown" commandName="&#x7c7b;..." description="&#x65b0;&#x5efa; Java &#x7c7b;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GA30GBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenPackageWizard" commandName="&#x5305;..." description="&#x65b0;&#x5efa; Java &#x5305;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GA30WBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenProjectWizard" commandName="Java &#x9879;&#x76ee;..." description="&#x65b0;&#x5efa; Java &#x9879;&#x76ee;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GBe4GBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.ui.SearchActionSet/org.eclipse.jdt.ui.actions.OpenJavaSearchPage" commandName="Java..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GBe4WBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jst.j2ee.J2eeMainActionSet/org.eclipse.jst.j2ee.internal.actions.NewJavaEEArtifact" commandName="Servlet" description="Create a new Java/Jakarta Servlet, optionally adding it to a project's deployment descriptor." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GCF8GBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jst.j2ee.J2eeMainActionSet/org.eclipse.jst.j2ee.internal.actions.NewJavaEEProject" commandName="&#x52a8;&#x6001; Web &#x9879;&#x76ee;" description="Create a Dynamic Web project. A Dynamic Web project supports developing Java and Jakarta Servlets, and deploys to Java and Jakarta Servlet Containers and J2EE, Java EE, and Jakarta EE certified application servers." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GCF8WBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.mylyn.java.actionSet.browsing/org.eclipse.mylyn.java.ui.actions.ApplyMylynToBrowsingPerspectiveAction" commandName="Focus Browsing Perspective" description="Focus Java Browsing Views on Active Task" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GCF8mBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.mylyn.doc.actionSet/org.eclipse.mylyn.tasks.ui.bug.report" commandName="&#x62a5;&#x544a;&#x95ee;&#x9898;&#x6216;&#x5efa;&#x8bae;" description="&#x62a5;&#x544a;&#x95ee;&#x9898;&#x6216;&#x5efa;&#x8bae;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GCtAGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="&#x6fc0;&#x6d3b;&#x524d;&#x4e00;&#x4e2a;&#x4efb;&#x52a1;" description="&#x6fc0;&#x6d3b;&#x524d;&#x4e00;&#x4e2a;&#x4efb;&#x52a1;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GCtAWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.pde.ui.SearchActionSet/org.eclipse.pde.ui.actions.OpenPluginSearchPage" commandName="&#x63d2;&#x4ef6;..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GCtAmBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="&#x5907;&#x5fd8;&#x5355;..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GCtA2BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="&#x641c;&#x7d22;..." description="&#x641c;&#x7d22;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GDUEGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="&#x540c;&#x6b65;..." description="&#x540c;&#x6b65;..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GDUEWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="&#x5171;&#x4eab;&#x9879;&#x76ee;..." description="&#x4f7f;&#x7528;&#x7248;&#x672c;&#x548c;&#x914d;&#x7f6e;&#x7ba1;&#x7406;&#x7cfb;&#x7edf;&#x4e0e;&#x5176;&#x4ed6;&#x4eba;&#x5171;&#x4eab;&#x8be5;&#x9879;&#x76ee;&#x3002;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GD7IGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="&#x5916;&#x90e8;&#x5de5;&#x5177;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GD7IWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.wst.server.ui.new.actionSet/org.eclipse.wst.server.ui.action.new.server" commandName="&#x521b;&#x5efa;&#x670d;&#x52a1;&#x5668;" description="&#x521b;&#x5efa;&#x670d;&#x52a1;&#x5668;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GD7ImBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.open" commandName="&#x6253;&#x5f00;Web&#x6d4f;&#x89c8;&#x5668;" description="&#x6253;&#x5f00;Web&#x6d4f;&#x89c8;&#x5668;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GD7I2BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.switch" commandName="Web &#x6d4f;&#x89c8;&#x5668;" description="Web &#x6d4f;&#x89c8;&#x5668;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GEiMGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.wst.web.ui.wizardsActionSet/org.eclipse.wst.web.ui.actions.newCSSFile" commandName="CSS" description="&#x521b;&#x5efa;&#x65b0;&#x7684;&#x7ea7;&#x8054;&#x6837;&#x5f0f;&#x8868;&#x3002;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GEiMWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.wst.web.ui.wizardsActionSet/org.eclipse.wst.web.ui.actions.newJSFile" commandName="JavaScript" description="&#x521b;&#x5efa;&#x4e00;&#x4e2a;&#x65b0;JavaScript&#x6587;&#x4ef6;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GEiMmBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.wst.web.ui.wizardsActionSet/org.eclipse.wst.web.ui.actions.newHTMLFile" commandName="HTML" description="&#x521b;&#x5efa;&#x4e00;&#x4e2a;&#x65b0;HTML&#x6587;&#x4ef6;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GFJQGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.wst.ws.explorer.explorer/org.eclipse.wst.ws.internal.explorer.action.LaunchWSEAction" commandName="Launch the Web Services Explorer" description="Launch the Web Services Explorer" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GFJQWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.ant.ui.BreakpointRulerActions/org.eclipse.ant.ui.actions.ManageBreakpointRulerAction" commandName="&#x5207;&#x6362;&#x65ad;&#x70b9;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GFJQmBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.datatools.sqltools.rullerDoubleClick/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Add Breakpoint" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GFJQ2BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.emf.exporter.genModelEditorContribution/org.eclipse.emf.exporter.ui.GenModelExportActionDelegate.Editor" commandName="Export Model..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GFJRGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.emf.importer.genModelEditorContribution/org.eclipse.emf.importer.ui.GenModelReloadActionDelegate.Editor" commandName="&#x91cd;&#x65b0;&#x88c5;&#x5165;..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GFwUGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.RemoveMappingActionID" commandName="&#x9664;&#x53bb;&#x6620;&#x5c04;" description="&#x9664;&#x53bb;&#x4e0e;&#x6240;&#x9009;&#x5bf9;&#x8c61;&#x76f8;&#x5173;&#x8054;&#x7684;&#x6620;&#x5c04;&#x3002;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GFwUWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.TypeMatchMappingActionID" commandName="&#x6309;&#x7c7b;&#x578b;&#x5339;&#x914d;&#x6620;&#x5c04;" description="&#x6839;&#x636e;&#x7c7b;&#x578b;&#x81ea;&#x52a8;&#x521b;&#x5efa;&#x5b50;&#x6620;&#x5c04;&#x3002;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GFwUmBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.NameMatchMappingActionID" commandName="&#x6309;&#x540d;&#x79f0;&#x5339;&#x914d;&#x6620;&#x5c04;" description="&#x6839;&#x636e;&#x540d;&#x79f0;&#x81ea;&#x52a8;&#x521b;&#x5efa;&#x5b50;&#x6620;&#x5c04;&#x3002;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GFwU2BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.CreateOneSidedMappingActionID" commandName="&#x521b;&#x5efa;&#x5355;&#x5411;&#x6620;&#x5c04;" description="&#x4e3a;&#x6240;&#x9009;&#x5bf9;&#x8c61;&#x521b;&#x5efa;&#x65b0;&#x6620;&#x5c04;&#x3002;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GGXYGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.CreateMappingActionID" commandName="&#x521b;&#x5efa;&#x6620;&#x5c04;" description="&#x5728;&#x6240;&#x9009;&#x7684;&#x5bf9;&#x8c61;&#x4e4b;&#x95f4;&#x521b;&#x5efa;&#x65b0;&#x6620;&#x5c04;&#x3002;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GGXYWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.ecore2ecore.action.AddOuputRootActionID" commandName="&#x6dfb;&#x52a0;&#x8f93;&#x51fa;&#x6839;..." description="&#x6dfb;&#x52a0;&#x65b0;&#x7684;&#x8f93;&#x51fa;&#x6839;&#x3002;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GGXYmBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.ecore2ecore.action.AddInputRootActionID" commandName="&#x6dfb;&#x52a0;&#x8f93;&#x5165;&#x6839;..." description="&#x6dfb;&#x52a0;&#x65b0;&#x7684;&#x8f93;&#x5165;&#x6839;&#x3002;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GG-cGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="&#x5207;&#x6362;&#x65ad;&#x70b9;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GG-cWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="&#x8fd0;&#x884c;&#x81f3;&#x884c;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GG-cmBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="&#x5207;&#x6362;&#x65ad;&#x70b9;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GG-c2BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.RunToLineRulerActionDelegate" commandName="&#x8fd0;&#x884c;&#x81f3;&#x884c;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GG-dGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetExecute" commandName="&#x6267;&#x884c;" description="&#x6267;&#x884c;&#x6240;&#x9009;&#x6587;&#x672c;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GHlgGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetDisplay" commandName="&#x663e;&#x793a;" description="&#x663e;&#x793a;&#x5bf9;&#x6240;&#x9009;&#x6587;&#x672c;&#x6c42;&#x503c;&#x7684;&#x7ed3;&#x679c;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GHlgWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetInspect" commandName="&#x68c0;&#x67e5;" description="&#x68c0;&#x67e5;&#x5bf9;&#x6240;&#x9009;&#x6587;&#x672c;&#x6c42;&#x503c;&#x7684;&#x7ed3;&#x679c;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GHlgmBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="Java &#x7f16;&#x8f91;&#x5668;&#x4e66;&#x7b7e;&#x6807;&#x5c3a;&#x64cd;&#x4f5c;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GHlg2BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java &#x7f16;&#x8f91;&#x5668;&#x6807;&#x5c3a;&#x5355;&#x51fb;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GIMkGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java &#x7f16;&#x8f91;&#x5668;&#x6807;&#x5c3a;&#x5355;&#x51fb;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GIMkWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="Java &#x7f16;&#x8f91;&#x5668;&#x4e66;&#x7b7e;&#x6807;&#x5c3a;&#x64cd;&#x4f5c;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GIMkmBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="Java &#x7f16;&#x8f91;&#x5668;&#x6807;&#x5c3a;&#x5355;&#x51fb;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GIMk2BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jst.jsp.core.jspsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="&#x6dfb;&#x52a0;&#x4e66;&#x7b7e;..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GIMlGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jst.jsp.core.jspsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="&#x9009;&#x62e9;&#x6807;&#x5c3a;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GIzoGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.textEditor.rulerActions/org.eclipse.lsp4e.debug.textEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GIzoWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.lsp4e.debug.genericEditor.rulerActions/org.eclipse.lsp4e.debug.genericEditor.doubleClickBreakpointAction" commandName="unusedlabel" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GIzomBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution/org.eclipse.m2e.jdt.ui.downloadSourcesAction" commandName="label" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GIzo2BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution_38/org.eclipse.m2e.jdt.ui.downloadSourcesAction_38" commandName="label" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GIzpGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="&#x6587;&#x672c;&#x7f16;&#x8f91;&#x5668;&#x4e66;&#x7b7e;&#x6807;&#x5c3a;&#x64cd;&#x4f5c;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GJasGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="&#x6587;&#x672c;&#x7f16;&#x8f91;&#x5668;&#x6807;&#x5c3a;&#x5355;&#x51fb;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GJasWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.wst.css.core.csssource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="&#x6dfb;&#x52a0;&#x4e66;&#x7b7e;..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GJasmBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.wst.css.core.csssource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="&#x9009;&#x62e9;&#x6807;&#x5c3a;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GJas2BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.wst.dtd.core.dtdsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="&#x6dfb;&#x52a0;&#x4e66;&#x7b7e;..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GJatGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.wst.dtd.core.dtdsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="&#x9009;&#x62e9;&#x6807;&#x5c3a;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GKBwGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.wst.html.core.htmlsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="&#x6dfb;&#x52a0;&#x4e66;&#x7b7e;..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GKBwWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.wst.html.core.htmlsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="&#x9009;&#x62e9;&#x6807;&#x5c3a;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GKBwmBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="JavaScript Editor Bookmark Ruler Action" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GKBw2BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="JavaScript Editor Ruler Single-Click" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GKBxGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="JavaScript Editor Ruler Single-Click" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GKBxWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="JavaScript Editor Bookmark Ruler Action" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GKo0GBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="JavaScript Editor Ruler Single-Click" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GKo0WBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.ui.articles.action.contribution.editor/org.eclipse.wst.wsdl.ui.actions.ReloadDependenciesActionDelegate" commandName="&#x91cd;&#x65b0;&#x88c5;&#x5165;&#x4f9d;&#x8d56;&#x9879;" description="&#x91cd;&#x65b0;&#x88c5;&#x5165;&#x4f9d;&#x8d56;&#x9879;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GKo0mBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.wst.wsdl.wsdlsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="&#x6dfb;&#x52a0;&#x4e66;&#x7b7e;..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GKo02BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.wst.wsdl.wsdlsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="&#x9009;&#x62e9;&#x6807;&#x5c3a;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GKo1GBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="&#x6dfb;&#x52a0;&#x4e66;&#x7b7e;..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GKo1WBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="&#x9009;&#x62e9;&#x6807;&#x5c3a;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GLP4GBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.wst.xsd.core.xsdsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="&#x6dfb;&#x52a0;&#x4e66;&#x7b7e;..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GLP4WBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.wst.xsd.core.xsdsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="&#x9009;&#x62e9;&#x6807;&#x5c3a;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GLP4mBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="&#x89c6;&#x56fe;&#x7ba1;&#x7406;..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GLP42BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="&#x9664;&#x53bb;&#x6240;&#x6709;&#x7ec8;&#x6b62;&#x7684;&#x64cd;&#x4f5c;" description="&#x9664;&#x53bb;&#x6240;&#x6709;&#x7ec8;&#x6b62;&#x7684;&#x542f;&#x52a8;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GL28GBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="&#x5168;&#x90e8;&#x6298;&#x53e0;" description="&#x5168;&#x90e8;&#x6298;&#x53e0;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GL28WBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="&#x5168;&#x90e8;&#x9664;&#x53bb;" description="&#x79fb;&#x9664;&#x6240;&#x6709;&#x65ad;&#x70b9;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GL28mBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="&#x4e0e;&#x201c;&#x8c03;&#x8bd5;&#x201d;&#x89c6;&#x56fe;&#x94fe;&#x63a5;" description="&#x4e0e;&#x201c;&#x8c03;&#x8bd5;&#x201d;&#x89c6;&#x56fe;&#x94fe;&#x63a5;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GL282BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="&#x5de5;&#x4f5c;&#x96c6;..." description="&#x7ba1;&#x7406;&#x5de5;&#x4f5c;&#x96c6;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GL29GBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="&#x53d6;&#x6d88;&#x9009;&#x62e9;&#x7f3a;&#x7701;&#x5de5;&#x4f5c;&#x96c6;" description="&#x53d6;&#x6d88;&#x9009;&#x62e9;&#x7f3a;&#x7701;&#x5de5;&#x4f5c;&#x96c6;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GMeAGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="&#x9009;&#x62e9;&#x7f3a;&#x7701;&#x5de5;&#x4f5c;&#x96c6;..." description="&#x9009;&#x62e9;&#x7f3a;&#x7701;&#x5de5;&#x4f5c;&#x96c6;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GMeAWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="&#x6392;&#x5e8f;&#x4f9d;&#x636e;" description="&#x6392;&#x5e8f;&#x4f9d;&#x636e;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GMeAmBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="&#x5206;&#x7ec4;&#x4f9d;&#x636e;" description="&#x663e;&#x793a;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GMeA2BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="&#x5168;&#x90e8;&#x9664;&#x53bb;" description="&#x79fb;&#x9664;&#x6240;&#x6709;&#x8868;&#x8fbe;&#x5f0f;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GMeBGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="&#x6dfb;&#x52a0;&#x67e5;&#x770b;&#x8868;&#x8fbe;&#x5f0f;..." description="&#x521b;&#x5efa;&#x65b0;&#x7684;&#x67e5;&#x770b;&#x8868;&#x8fbe;&#x5f0f;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GNFEGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="&#x9501;&#x5b9a;&#x5185;&#x5b58;&#x76d1;&#x89c6;&#x5668;" description="&#x9501;&#x5b9a;&#x5185;&#x5b58;&#x76d1;&#x89c6;&#x5668;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GNFEWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="&#x65b0;&#x5efa;&#x201c;&#x5185;&#x5b58;&#x201d;&#x89c6;&#x56fe;" description="&#x65b0;&#x5efa;&#x201c;&#x5185;&#x5b58;&#x201d;&#x89c6;&#x56fe;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GNFEmBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="&#x5207;&#x6362;&#x5185;&#x5b58;&#x76d1;&#x89c6;&#x5668;&#x7a97;&#x683c;" description="&#x5207;&#x6362;&#x5185;&#x5b58;&#x76d1;&#x89c6;&#x5668;&#x7a97;&#x683c;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GNFE2BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="&#x94fe;&#x63a5;&#x5185;&#x5b58;&#x5448;&#x793a;&#x7a97;&#x683c;" description="&#x94fe;&#x63a5;&#x5185;&#x5b58;&#x5448;&#x793a;&#x7a97;&#x683c;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GNsIGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="&#x8868;&#x5448;&#x793a;&#x9996;&#x9009;&#x9879;..." description="&#x8868;&#x5448;&#x793a;&#x9996;&#x9009;&#x9879;(&amp;T)..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GNsIWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="&#x5207;&#x6362;&#x5206;&#x5272;&#x7684;&#x7a97;&#x683c;" description="&#x5207;&#x6362;&#x5206;&#x5272;&#x7684;&#x7a97;&#x683c;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GNsImBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="&#x5207;&#x6362;&#x5185;&#x5b58;&#x76d1;&#x89c6;&#x5668;" description="&#x5207;&#x6362;&#x5185;&#x5b58;&#x76d1;&#x89c6;&#x5668;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GNsI2BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="&#x9996;&#x9009;&#x9879;..." description="&#x9996;&#x9009;&#x9879;(&amp;P)..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GOTMGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java &#x9996;&#x9009;&#x9879;..." description="&#x6253;&#x5f00; Java &#x53d8;&#x91cf;&#x7684;&#x9996;&#x9009;&#x9879;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GOTMWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variablesViewActions.AllReferencesInView" commandName="&#x663e;&#x793a;&#x5f15;&#x7528;" description="&#x5c06;&#x5bf9;&#x53d8;&#x91cf;&#x89c6;&#x56fe;&#x4e2d;&#x7684;&#x6bcf;&#x4e2a;&#x5bf9;&#x8c61;&#x7684;&#x5f15;&#x7528;&#x663e;&#x793a;&#x4e3a;&#x4e00;&#x7ec4;&#x5bf9;&#x8c61;&#x3002;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GOTMmBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="&#x663e;&#x793a;&#x7a7a;&#x7684;&#x6570;&#x7ec4;&#x6761;&#x76ee;" description="&#x663e;&#x793a;&#x7a7a;&#x7684;&#x6570;&#x7ec4;&#x6761;&#x76ee;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GOTM2BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowQualified" commandName="&#x663e;&#x793a;&#x9650;&#x5b9a;&#x540d;" description="&#x663e;&#x793a;&#x9650;&#x5b9a;&#x540d;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GOTNGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowStatic" commandName="&#x663e;&#x793a;&#x9759;&#x6001;&#x53d8;&#x91cf;" description="&#x663e;&#x793a;&#x9759;&#x6001;&#x53d8;&#x91cf;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GO6QGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowConstants" commandName="&#x663e;&#x793a;&#x5e38;&#x91cf;" description="&#x663e;&#x793a;&#x5e38;&#x91cf;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GO6QWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java &#x9996;&#x9009;&#x9879;..." description="&#x6253;&#x5f00; Java &#x53d8;&#x91cf;&#x7684;&#x9996;&#x9009;&#x9879;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GO6QmBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.AllReferencesInView" commandName="&#x663e;&#x793a;&#x5f15;&#x7528;" description="&#x663e;&#x793a;&#x5f15;&#x7528;(&amp;R)" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GO6Q2BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="&#x663e;&#x793a;&#x7a7a;&#x7684;&#x6570;&#x7ec4;&#x6761;&#x76ee;" description="&#x663e;&#x793a;&#x7a7a;&#x7684;&#x6570;&#x7ec4;&#x6761;&#x76ee;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GO6RGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowQualified" commandName="&#x663e;&#x793a;&#x9650;&#x5b9a;&#x540d;" description="&#x663e;&#x793a;&#x9650;&#x5b9a;&#x540d;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GPhUGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowStatic" commandName="&#x663e;&#x793a;&#x9759;&#x6001;&#x53d8;&#x91cf;" description="&#x663e;&#x793a;&#x9759;&#x6001;&#x53d8;&#x91cf;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GPhUWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowConstants" commandName="&#x663e;&#x793a;&#x5e38;&#x91cf;" description="&#x663e;&#x793a;&#x5e38;&#x91cf;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GPhUmBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.actions.AddException" commandName="&#x6dfb;&#x52a0; Java &#x5f02;&#x5e38;&#x65ad;&#x70b9;" description="&#x6dfb;&#x52a0; Java &#x5f02;&#x5e38;&#x65ad;&#x70b9;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GPhU2BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="&#x663e;&#x793a;&#x9650;&#x5b9a;&#x540d;" description="&#x663e;&#x793a;&#x9650;&#x5b9a;&#x540d;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GPhVGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowThreadGroups" commandName="&#x663e;&#x793a;&#x7ebf;&#x7a0b;&#x7ec4;" description="&#x663e;&#x793a;&#x7ebf;&#x7a0b;&#x7ec4;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GQIYGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowQualified" commandName="&#x663e;&#x793a;&#x9650;&#x5b9a;&#x540d;" description="&#x663e;&#x793a;&#x9650;&#x5b9a;&#x540d;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GQIYWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowSystemThreads" commandName="&#x663e;&#x793a;&#x7cfb;&#x7edf;&#x7ebf;&#x7a0b;" description="&#x663e;&#x793a;&#x7cfb;&#x7edf;&#x7ebf;&#x7a0b;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GQIYmBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowRunningThreads" commandName="Show Running Threads" description="Show Running Threads" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GQIY2BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowMonitorThreadInfo" commandName="&#x663e;&#x793a;&#x76d1;&#x89c6;&#x5668;" description="&#x663e;&#x793a;&#x7ebf;&#x7a0b;&#x76d1;&#x89c6;&#x5668;&#x4fe1;&#x606f;(&amp;M)" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GQIZGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Watch" commandName="&#x521b;&#x5efa;&#x67e5;&#x770b;&#x8868;&#x8fbe;&#x5f0f;" description="&#x4ece;&#x6240;&#x9009;&#x6587;&#x672c;&#x521b;&#x5efa;&#x67e5;&#x770b;&#x8868;&#x8fbe;&#x5f0f;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GQvcGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Execute" commandName="&#x6267;&#x884c;" description="&#x6267;&#x884c;&#x6240;&#x9009;&#x6587;&#x672c;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GQvcWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Display" commandName="&#x663e;&#x793a;" description="&#x663e;&#x793a;&#x5bf9;&#x6240;&#x9009;&#x6587;&#x672c;&#x6c42;&#x503c;&#x7684;&#x7ed3;&#x679c;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GQvcmBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Inspect" commandName="&#x68c0;&#x67e5;" description="&#x68c0;&#x67e5;&#x5bf9;&#x6240;&#x9009;&#x6587;&#x672c;&#x6c42;&#x503c;&#x7684;&#x7ed3;&#x679c;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GQvc2BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="&#x805a;&#x7126;&#x4e8e;&#x6d3b;&#x52a8;&#x4efb;&#x52a1;" description="&#x805a;&#x7126;&#x4e8e;&#x6d3b;&#x52a8;&#x4efb;&#x52a1;&#xff08;Alt+&#x5355;&#x51fb;&#x53ef;&#x4ee5;&#x663e;&#x793a;&#x88ab;&#x8fc7;&#x6ee4;&#x7684;&#x5143;&#x7d20;&#xff09;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GQvdGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.mylyn.java.ui.markers.breakpoints.contribution/org.eclipse.mylyn.java.ui.actions.focus.markers.breakpoints" commandName="&#x805a;&#x7126;&#x4e8e;&#x6d3b;&#x52a8;&#x4efb;&#x52a1;" description="&#x805a;&#x7126;&#x4e8e;&#x6d3b;&#x52a8;&#x4efb;&#x52a1;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GRWgGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.mylyn.ui.debug.view.contribution/org.eclipse.mylyn.ui.actions.FilterResourceNavigatorAction" commandName="Focus on Active Task (Experimental)" description="Focus on Active Task (Experimental)" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GRWgWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.mylyn.ui.projectexplorer.filter/org.eclipse.mylyn.ide.ui.actions.focus.projectExplorer" commandName="&#x805a;&#x7126;&#x4e8e;&#x6d3b;&#x52a8;&#x4efb;&#x52a1;" description="&#x805a;&#x7126;&#x4e8e;&#x6d3b;&#x52a8;&#x4efb;&#x52a1;&#xff08;Alt+&#x5355;&#x51fb;&#x53ef;&#x4ee5;&#x663e;&#x793a;&#x88ab;&#x8fc7;&#x6ee4;&#x7684;&#x5143;&#x7d20;&#xff09;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GRWgmBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.mylyn.ui.search.contribution/org.eclipse.mylyn.ide.ui.actions.focus.search.results" commandName="&#x805a;&#x7126;&#x4e8e;&#x6d3b;&#x52a8;&#x4efb;&#x52a1;" description="&#x805a;&#x7126;&#x4e8e;&#x6d3b;&#x52a8;&#x4efb;&#x52a1;&#xff08;Alt+&#x5355;&#x51fb;&#x53ef;&#x4ee5;&#x663e;&#x793a;&#x88ab;&#x8fc7;&#x6ee4;&#x7684;&#x5143;&#x7d20;&#xff09;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GRWg2BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.mylyn.ui.resource.navigator.filter/org.eclipse.mylyn.ide.ui.actions.focus.resourceNavigator" commandName="&#x805a;&#x7126;&#x4e8e;&#x6d3b;&#x52a8;&#x4efb;&#x52a1;" description="&#x805a;&#x7126;&#x4e8e;&#x6d3b;&#x52a8;&#x4efb;&#x52a1;&#xff08;Alt+&#x5355;&#x51fb;&#x53ef;&#x4ee5;&#x663e;&#x793a;&#x88ab;&#x8fc7;&#x6ee4;&#x7684;&#x5143;&#x7d20;&#xff09;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GRWhGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.mylyn.problems.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.problems" commandName="&#x805a;&#x7126;&#x4e8e;&#x6d3b;&#x52a8;&#x4efb;&#x52a1;" description="&#x805a;&#x7126;&#x4e8e;&#x6d3b;&#x52a8;&#x4efb;&#x52a1;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GR9kGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.mylyn.markers.all.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.all" commandName="&#x805a;&#x7126;&#x4e8e;&#x6d3b;&#x52a8;&#x4efb;&#x52a1;" description="&#x805a;&#x7126;&#x4e8e;&#x6d3b;&#x52a8;&#x4efb;&#x52a1;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GR9kWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.mylyn.markers.tasks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.tasks" commandName="&#x805a;&#x7126;&#x4e8e;&#x6d3b;&#x52a8;&#x4efb;&#x52a1;" description="&#x805a;&#x7126;&#x4e8e;&#x6d3b;&#x52a8;&#x4efb;&#x52a1;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GR9kmBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.mylyn.markers.bookmarks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.bookmarks" commandName="&#x805a;&#x7126;&#x4e8e;&#x6d3b;&#x52a8;&#x4efb;&#x52a1;" description="&#x805a;&#x7126;&#x4e8e;&#x6d3b;&#x52a8;&#x4efb;&#x52a1;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GR9k2BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.mylyn.java.explorer.contribution/org.eclipse.mylyn.java.actions.focus.packageExplorer" commandName="&#x805a;&#x7126;&#x4e8e;&#x6d3b;&#x52a8;&#x4efb;&#x52a1;" description="&#x805a;&#x7126;&#x4e8e;&#x6d3b;&#x52a8;&#x4efb;&#x52a1;&#xff08;Alt+&#x5355;&#x51fb;&#x53ef;&#x4ee5;&#x663e;&#x793a;&#x88ab;&#x8fc7;&#x6ee4;&#x7684;&#x5143;&#x7d20;&#xff09;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GR9lGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.search.open" commandName="&#x641c;&#x7d22;&#x4ed3;&#x5e93;..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GSkoGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="&#x540c;&#x6b65;&#x66f4;&#x6539;" description="&#x540c;&#x6b65;&#x66f4;&#x6539;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GSkoWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="&#x4ece;&#x5386;&#x53f2;&#x6062;&#x590d;&#x4efb;&#x52a1;..." category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GSkomBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="&#x663e;&#x793a;&#x4efb;&#x52a1;&#x4ed3;&#x5e93;&#x89c6;&#x56fe;" description="&#x663e;&#x793a;&#x4efb;&#x52a1;&#x4ed3;&#x5e93;&#x89c6;&#x56fe;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GSko2BSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="&#x663e;&#x793a;&#x754c;&#x9762;&#x56fe;&#x4f8b;" description="&#x663e;&#x793a;&#x4efb;&#x52a1;&#x754c;&#x9762;&#x56fe;&#x4f8b;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GSkpGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="&#x805a;&#x7126;&#x4e8e;&#x6bcf;&#x5468;&#x5de5;&#x4f5c;" description="&#x805a;&#x7126;&#x4e8e;&#x6bcf;&#x5468;&#x5de5;&#x4f5c;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GTLsGBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.pde.ui.logViewActions/org.eclipse.jdt.debug.ui.LogViewActions.showStackTrace" commandName="&#x5728;&#x63a7;&#x5236;&#x53f0;&#x4e2d;&#x663e;&#x793a;&#x5806;&#x6808;&#x8ddf;&#x8e2a;" description="&#x5728;&#x63a7;&#x5236;&#x53f0;&#x4e2d;&#x663e;&#x793a;&#x5806;&#x6808;&#x8ddf;&#x8e2a;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <commands xmi:id="_6GTLsWBSEfCWTN1FKOuw5w" elementId="AUTOGEN:::org.eclipse.ui.articles.action.contribution.view/org.eclipse.wst.wsi.ui.internal.actions.actionDelegates.ValidateWSIProfileActionDelegate" commandName="WS-I Profile Validator" description="&#x9a8c;&#x8bc1; WS-I &#x6d88;&#x606f;&#x65e5;&#x5fd7;&#x6587;&#x4ef6;" category="_51UwiWBSEfCWTN1FKOuw5w"/>
  <addons xmi:id="_504rk2BSEfCWTN1FKOuw5w" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_504rlGBSEfCWTN1FKOuw5w" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_504rlWBSEfCWTN1FKOuw5w" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_504rlmBSEfCWTN1FKOuw5w" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_504rl2BSEfCWTN1FKOuw5w" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_504rmGBSEfCWTN1FKOuw5w" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_504rmWBSEfCWTN1FKOuw5w" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_504rmmBSEfCWTN1FKOuw5w" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_504rm2BSEfCWTN1FKOuw5w" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_504rnGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_51BOcGBSEfCWTN1FKOuw5w" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_IYS0IKimEeS11vbz3f9ezw" elementId="org.eclipse.ui.ide.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_dz0JgGOlEeSMMaPQU2nlzw" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_51UwcGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.category.edit" name="&#x7f16;&#x8f91;"/>
  <categories xmi:id="_51UwcWBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_51UwcmBSEfCWTN1FKOuw5w" elementId="org.eclipse.buildship.ui.project" name="Buildship" description="Contains the Buildship specific commands"/>
  <categories xmi:id="_51Uwc2BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.builds.ui.category.Commands" name="Builds"/>
  <categories xmi:id="_51UwdGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.category.textEditor" name="&#x6587;&#x672c;&#x7f16;&#x8f91;" description="&#x6587;&#x672c;&#x7f16;&#x8f91;&#x547d;&#x4ee4;"/>
  <categories xmi:id="_51UwdWBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_51UwdmBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.jsdt.ui.category.source" name="&#x6e90;" description="JavaScript Source Actions"/>
  <categories xmi:id="_51Uwd2BSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.commands" name="&#x4efb;&#x52a1;&#x4ed3;&#x5e93;"/>
  <categories xmi:id="_51UweGBSEfCWTN1FKOuw5w" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_51UweWBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.server.ui" name="&#x670d;&#x52a1;&#x5668;" description="&#x670d;&#x52a1;&#x5668;"/>
  <categories xmi:id="_51UwemBSEfCWTN1FKOuw5w" elementId="org.eclipse.eclemma.ui" name="EclEmma Code Coverage"/>
  <categories xmi:id="_51Uwe2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.category.file" name="&#x6587;&#x4ef6;"/>
  <categories xmi:id="_51UwfGBSEfCWTN1FKOuw5w" elementId="org.eclipse.text.quicksearch.commands.category" name="&#x5feb;&#x901f;&#x641c;&#x7d22;"/>
  <categories xmi:id="_51UwfWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.category.window" name="&#x7a97;&#x53e3;"/>
  <categories xmi:id="_51UwfmBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.result.category" name="SQL &#x7ed3;&#x679c;&#x89c6;&#x56fe;"/>
  <categories xmi:id="_51Uwf2BSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.genericeditor.extension.category.source" name="Target Definition Source" description="Target Definition Source Page actions"/>
  <categories xmi:id="_51UwgGBSEfCWTN1FKOuw5w" elementId="org.eclipse.debug.ui.category.run" name="&#x8fd0;&#x884c;&#xff0f;&#x8c03;&#x8bd5;" description="&#x8fd0;&#x884c;&#xff0f;&#x8c03;&#x8bd5;&#x547d;&#x4ee4;&#x7c7b;&#x522b;"/>
  <categories xmi:id="_51UwgWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.category.dialogs" name="&#x5bf9;&#x8bdd;&#x6846;" description="&#x7528;&#x4e8e;&#x6253;&#x5f00;&#x5bf9;&#x8bdd;&#x6846;&#x7684;&#x547d;&#x4ee4;"/>
  <categories xmi:id="_51UwgmBSEfCWTN1FKOuw5w" elementId="org.eclipse.oomph" name="Oomph"/>
  <categories xmi:id="_51Uwg2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.ui.jpaMetadataConversionCommands" name="JPA Metadata Conversion"/>
  <categories xmi:id="_51UwhGBSEfCWTN1FKOuw5w" elementId="org.eclipse.wst.xml.views.XPathView" name="XPath"/>
  <categories xmi:id="_51UwhWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jpt.jpa.ui.jpaStructureViewCommands" name="JPA&#x7ed3;&#x6784;&#x89c6;&#x56fe;"/>
  <categories xmi:id="_51UwhmBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_51Uwh2BSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.ui.category.source" name="&#x6e05;&#x5355;&#x7f16;&#x8f91;&#x5668;&#x6e90;&#x4ee3;&#x7801;" description="PDE &#x6e90;&#x4ee3;&#x7801;&#x9875;&#x9762;&#x64cd;&#x4f5c;"/>
  <categories xmi:id="_51UwiGBSEfCWTN1FKOuw5w" elementId="org.eclipse.oomph.commands" name="Oomph"/>
  <categories xmi:id="_51UwiWBSEfCWTN1FKOuw5w" elementId="org.eclipse.core.commands.categories.autogenerated" name="&#x53d6;&#x6d88;&#x5206;&#x7c7b;" description="&#x547d;&#x4ee4;&#x662f;&#x81ea;&#x52a8;&#x751f;&#x6210;&#x7684;&#xff0c;&#x6216;&#x8005;&#x6ca1;&#x6709;&#x5206;&#x7c7b;"/>
  <categories xmi:id="_51UwimBSEfCWTN1FKOuw5w" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_51Uwi2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.category.views" name="&#x89c6;&#x56fe;" description="&#x6253;&#x5f00;&#x89c6;&#x56fe;&#x7684;&#x547d;&#x4ee4;"/>
  <categories xmi:id="_51UwjGBSEfCWTN1FKOuw5w" elementId="org.eclipse.tm4e.languageconfiguration.category" name="TM4E Language Configuration"/>
  <categories xmi:id="_51UwjWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jst.pagedesigner.pagelayout" name="Web Page Editor Layout"/>
  <categories xmi:id="_51UwjmBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="&#x4efb;&#x52a1;&#x7f16;&#x8f91;&#x5668;"/>
  <categories xmi:id="_51Uwj2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.ide.markerContents" name="&#x5185;&#x5bb9;" description="&#x83dc;&#x5355;&#x5185;&#x5bb9;&#x7684;&#x7c7b;&#x522b;"/>
  <categories xmi:id="_51UwkGBSEfCWTN1FKOuw5w" elementId="org.eclipse.codegen.ui.jet.refactor" name="JET Refactor Actions"/>
  <categories xmi:id="_51UwkWBSEfCWTN1FKOuw5w" elementId="org.eclipse.oomph.setup.category" name="Oomph Setup"/>
  <categories xmi:id="_51UwkmBSEfCWTN1FKOuw5w" elementId="org.eclipse.codegen.ui.jet.source" name="JET Source Actions"/>
  <categories xmi:id="_51Uwk2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.category.navigate" name="&#x5bfc;&#x822a;"/>
  <categories xmi:id="_51UwlGBSEfCWTN1FKOuw5w" elementId="org.eclipse.mylyn.java.ui.commands" name="Java Context" description="Java Task-Focused Interface Commands"/>
  <categories xmi:id="_51UwlWBSEfCWTN1FKOuw5w" elementId="org.eclipse.lsp4e.category" name="&#x8bed;&#x8a00;&#x670d;&#x52a1;&#x5668;"/>
  <categories xmi:id="_51UwlmBSEfCWTN1FKOuw5w" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_51Uwl2BSEfCWTN1FKOuw5w" elementId="org.eclipse.compare.ui.category.compare" name="&#x6bd4;&#x8f83;" description="&#x6bd4;&#x8f83;&#x547d;&#x4ee4;&#x7c7b;&#x522b;"/>
  <categories xmi:id="_51UwmGBSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaedtor.10x" name="ASA 9.x table schema editor"/>
  <categories xmi:id="_51UwmWBSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.category.refactoring" name="&#x91cd;&#x6784;&#x5668; &#xff0d; Java" description="Java &#x91cd;&#x6784;&#x64cd;&#x4f5c;"/>
  <categories xmi:id="_51UwmmBSEfCWTN1FKOuw5w" elementId="org.eclipse.emf.codegen.ecore.ui.Commands" name="EMF &#x6a21;&#x677f;&#x4ee3;&#x7801;&#x751f;&#x6210;&#x5668;" description="Commands for the EMF code generation tools"/>
  <categories xmi:id="_51Uwm2BSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.category.help" name="&#x5e2e;&#x52a9;"/>
  <categories xmi:id="_51UwnGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.category.project" name="&#x9879;&#x76ee;"/>
  <categories xmi:id="_51VXgGBSEfCWTN1FKOuw5w" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_51VXgWBSEfCWTN1FKOuw5w" elementId="org.eclipse.search.ui.category.search" name="&#x641c;&#x7d22;" description="&#x641c;&#x7d22;&#x547d;&#x4ee4;&#x7c7b;&#x522b;"/>
  <categories xmi:id="_51VXgmBSEfCWTN1FKOuw5w" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_51VXg2BSEfCWTN1FKOuw5w" elementId="org.eclipse.datatools.sqltools.sqleditor.category" name="&#x6570;&#x636e;&#x5e93;&#x5de5;&#x5177;" description="&#x6570;&#x636e;&#x5e93;&#x5f00;&#x53d1;&#x5de5;&#x5177;"/>
  <categories xmi:id="_51VXhGBSEfCWTN1FKOuw5w" elementId="org.eclipse.ui.category.perspectives" name="&#x900f;&#x89c6;&#x56fe;" description="&#x6253;&#x5f00;&#x900f;&#x89c6;&#x56fe;&#x7684;&#x547d;&#x4ee4;"/>
  <categories xmi:id="_51VXhWBSEfCWTN1FKOuw5w" elementId="org.eclipse.ltk.ui.category.refactoring" name="&#x91cd;&#x6784;"/>
  <categories xmi:id="_51VXhmBSEfCWTN1FKOuw5w" elementId="org.eclipse.gef.category.view" name="View" description="View"/>
  <categories xmi:id="_51VXh2BSEfCWTN1FKOuw5w" elementId="org.eclipse.jdt.ui.category.source" name="&#x6e90;" description="Java &#x6e90;&#x7801;&#x64cd;&#x4f5c;"/>
  <categories xmi:id="_51VXiGBSEfCWTN1FKOuw5w" elementId="org.eclipse.pde.runtime.spy.commands.category" name="&#x5bc6;&#x63a2;"/>
</application:Application>
