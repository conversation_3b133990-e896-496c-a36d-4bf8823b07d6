package com.ruoyi.web.controller.api;

import javax.servlet.http.HttpServletRequest;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.shiro.service.SysRegisterService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.web.controller.api.model.LoginRequest;
import com.ruoyi.web.controller.api.model.RegisterRequest;

/**
 * 移动端登录、注册接口
 */
@RestController
@RequestMapping("/api")
public class ApiAuthController extends BaseController {

    @Autowired
    private SysRegisterService registerService;

    @Autowired
    private ISysConfigService configService;

    /** 登录 */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginRequest req, HttpServletRequest request) {
        if (StringUtils.isEmpty(req.getUsername()) || StringUtils.isEmpty(req.getPassword())) {
            return AjaxResult.error("用户名或密码不能为空");
        }
        UsernamePasswordToken token = new UsernamePasswordToken(req.getUsername(), req.getPassword(), Boolean.TRUE.equals(req.getRememberMe()));
        Subject subject = SecurityUtils.getSubject();
        try {
            subject.login(token);
            AjaxResult res = AjaxResult.success("登录成功");
            res.put("code", 0);
            AjaxResult data = new AjaxResult();
            data.put("accessToken", subject.getSession().getId().toString());
            data.put("expiresTime", subject.getSession().getTimeout());
            data.put("userId", getUserId());
            res.put("data", data);
            return res;
        } catch (AuthenticationException e) {
            AjaxResult err = AjaxResult.error(StringUtils.isEmpty(e.getMessage()) ? "用户或密码错误" : e.getMessage());
            err.put("code", 1);
            return err;
        }
    }

    /** 注册 */
    @PostMapping("/register")
    public AjaxResult register(@RequestBody RegisterRequest req) {
        if (!"true".equals(configService.selectConfigByKey("sys.account.registerUser"))) {
            return AjaxResult.error("当前系统未开放注册功能");
        }
        if (StringUtils.isEmpty(req.getUsername()) || StringUtils.isEmpty(req.getPassword())) {
            return AjaxResult.error("用户名或密码不能为空");
        }
        SysUser user = new SysUser();
        user.setLoginName(req.getUsername());
        user.setPassword(req.getPassword());
        user.setPhonenumber(req.getPhonenumber());
        user.setEmail(req.getEmail());
        user.setUserName(req.getUsername());
        String msg = registerService.register(user);
        if (StringUtils.isEmpty(msg)) {
            AjaxResult ok = AjaxResult.success("注册成功");
            ok.put("code", 0);
            return ok;
        } else {
            AjaxResult err = AjaxResult.error(msg);
            err.put("code", 1);
            return err;
        }
    }
}
