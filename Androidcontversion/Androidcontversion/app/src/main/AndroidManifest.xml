<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 文件访问权限 - 适用于不同Android版本 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" 
                     android:maxSdkVersion="32" />
    <!-- Android 13及以上版本的权限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" 
                     android:minSdkVersion="33" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" 
                     android:minSdkVersion="33" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" 
                     android:minSdkVersion="33" />
    <!-- 写入外部存储权限，用于保存修复后的文件 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
                     android:maxSdkVersion="29" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@drawable/ic_app_icon_new"
        android:label="@string/app_name"
        android:roundIcon="@drawable/ic_app_icon_new"
        android:supportsRtl="true"
        android:theme="@style/Theme.Shoujixiufu"
        android:requestLegacyExternalStorage="true"
        tools:targetApi="31">
        <activity
            android:name=".SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".MainActivity"
            android:exported="false">
        </activity>
        <activity 
            android:name=".AboutUsActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" 
            android:label="@string/about_us" />
        <activity 
            android:name=".LogoViewerActivity"
            android:exported="false" />
        <activity 
            android:name=".LoadingActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity 
            android:name=".AudioScanActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity 
            android:name=".CaseDetailActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity 
            android:name=".CasesActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity 
            android:name=".ContactServiceActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar"
            android:windowSoftInputMode="adjustResize" />
        <activity 
            android:name=".FeedbackActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity 
            android:name=".FileScanActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity 
            android:name=".OrderActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity 
            android:name=".ProfileActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity 
            android:name=".SettingsActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity 
            android:name=".ServiceAgreementActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity 
            android:name=".ScanActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity 
            android:name=".RecoveryReminderActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".SupervisionComplaintActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".TransferActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity
            android:name=".VideoRepairActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity
            android:name=".VideoScanActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity
            android:name=".PaymentActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity
            android:name=".PaymentSuccessActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity
            android:name=".PrivacyPolicyActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity
            android:name=".ImageCropActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity
            android:name=".ImageScaleActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity
            android:name=".FileSendActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity
            android:name=".FileRepairActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity
            android:name=".AudioRepairActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity
            android:name=".TutorialActivity"
            android:exported="false"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity
            android:name=".VipMembershipActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
        <activity
            android:name=".VipMembershipActivityNew"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Shoujixiufu.NoActionBar" />
    </application>

</manifest>