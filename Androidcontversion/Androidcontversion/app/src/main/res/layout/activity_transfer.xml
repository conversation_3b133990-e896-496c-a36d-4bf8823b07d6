<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".TransferActivity">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- 状态栏 -->
        <RelativeLayout
            android:id="@+id/statusBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:paddingHorizontal="20dp"
            android:paddingVertical="10dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:text="HD 4G"
                android:textColor="@color/text_primary"
                android:textSize="14sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:text="12:31"
                android:textColor="@color/text_primary"
                android:textSize="14sp" />
        </RelativeLayout>

        <!-- 头部 -->
        <RelativeLayout
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/statusBar"
            android:background="@color/white"
            android:padding="20dp">

            <ImageButton
                android:id="@+id/backBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:background="@null"
                android:contentDescription="@string/back"
                android:src="@drawable/ic_arrow_back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/transfer_file"
                android:textColor="@color/text_primary"
                android:textSize="20sp"
                android:textStyle="bold" />

            <ImageButton
                android:id="@+id/refreshBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:background="@null"
                android:contentDescription="@string/refresh"
                android:src="@drawable/ic_refresh" />
        </RelativeLayout>

        <!-- 传送选项 -->
        <RelativeLayout
            android:id="@+id/transferContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/header"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="80dp"
            android:paddingHorizontal="40dp">

            <androidx.cardview.widget.CardView
                android:id="@+id/transferOptions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="20dp"
                app:cardElevation="10dp"
                app:contentPadding="20dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:id="@+id/sendOption"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:padding="25dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_toStartOf="@id/sendArrow"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/send_files"
                                android:textColor="@color/text_primary"
                                android:textSize="20sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="@string/send_files_desc"
                                android:textColor="@color/text_tertiary"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/sendArrow"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:text="▶"
                            android:textColor="@color/success_color"
                            android:textSize="24sp" />

                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/divider" />

                    <RelativeLayout
                        android:id="@+id/receiveOption"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:padding="25dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_toStartOf="@id/receiveArrow"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/receive_files"
                                android:textColor="@color/text_primary"
                                android:textSize="20sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="@string/receive_files_desc"
                                android:textColor="@color/text_tertiary"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/receiveArrow"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:text="◀"
                            android:textColor="@color/blue_primary"
                            android:textSize="24sp" />

                    </RelativeLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </RelativeLayout>

        <!-- 底部系统导航 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_alignParentBottom="true"
            android:background="@android:color/black"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/navBack"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="30dp"
                android:text="◀"
                android:textColor="@color/white"
                android:textSize="24sp" />

            <TextView
                android:id="@+id/navHome"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="30dp"
                android:text="○"
                android:textColor="@color/white"
                android:textSize="20sp" />

            <TextView
                android:id="@+id/navTasks"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="30dp"
                android:text="▢"
                android:textColor="@color/white"
                android:textSize="16sp" />
        </LinearLayout>
    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 