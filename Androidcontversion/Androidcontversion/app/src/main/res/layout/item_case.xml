<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    android:paddingTop="20dp"
    android:paddingBottom="20dp">

    <LinearLayout
        android:id="@+id/case_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="15dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/case_image"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/case_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:ellipsize="end"
            android:lineSpacingExtra="2dp"
            android:maxLines="3"
            android:textColor="@color/black"
            android:textSize="16sp"
            tools:text="在手机相册中浏览照片时，不小心滑动屏幕导致一些重要的照片被误删除。" />

        <TextView
            android:id="@+id/case_date"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#999999"
            android:textSize="12sp"
            tools:text="2023-12-29 13:15:26" />

    </LinearLayout>

    <ImageView
        android:id="@+id/case_image"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:background="#F5F5F5"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@tools:sample/avatars" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="20dp"
        android:background="#F0F0F0"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 