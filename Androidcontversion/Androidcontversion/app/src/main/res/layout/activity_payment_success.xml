<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".PaymentSuccessActivity">

    <!-- 头部背景已删除 -->

    <!-- 主内容区域 -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 成功内容 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:paddingVertical="40dp"
                android:paddingHorizontal="20dp">

                <FrameLayout
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_marginBottom="30dp">

                    <ImageView
                        android:id="@+id/success_icon"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/blue_gradient_circle_bg"
                        android:padding="28dp"
                        android:src="@drawable/ic_check_circle_large"
                        app:tint="@color/white" />
                </FrameLayout>

                <TextView
                    android:id="@+id/success_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="支付成功"
                    android:textColor="#333333"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="20dp" />

                <TextView
                    android:id="@+id/success_subtitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="+恭喜支付成功+"
                    android:textColor="@color/white"
                    android:background="@drawable/orange_gradient_button_bg"
                    android:paddingHorizontal="24dp"
                    android:paddingVertical="12dp"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="30dp" />
            </LinearLayout>

            <!-- 手机号码表单 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="15dp"
                app:cardElevation="5dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="手机号码：方便工程师好与您联系"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:gravity="center"
                        android:layout_marginBottom="10dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="仅差最后一步，请您填写正确的手机号码，\n以便工程师为您服务"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        android:gravity="center"
                        android:lineSpacingExtra="5dp"
                        android:layout_marginBottom="25dp" />

                    <EditText
                        android:id="@+id/phone_input"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="请输入您的手机号码"
                        android:inputType="phone"
                        android:padding="15dp"
                        android:background="@drawable/edit_text_bg"
                        android:textSize="16sp"
                        android:layout_marginBottom="20dp" />

                    <Button
                        android:id="@+id/submit_btn"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="提交"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:background="@drawable/green_gradient_button_bg"
                        android:padding="15dp"
                        android:layout_marginBottom="10dp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 温馨提示 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="15dp"
                app:cardElevation="5dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="温馨提示"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="15dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="• "
                            android:textColor="#2ed573"
                            android:textStyle="bold"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="预留手机号码是为了方便与您对接"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="• "
                            android:textColor="#2ed573"
                            android:textStyle="bold"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="可根据教程或在线客服协助服务"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="• "
                            android:textColor="#2ed573"
                            android:textStyle="bold"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="可联系在线客服开具增值税发票(支持电子发票)"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 服务信息 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="#fff3cd"
                android:padding="15dp"
                android:layout_marginHorizontal="20dp"
                android:layout_marginBottom="30dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="⚠️ 数据网络开启可能会消耗流量，请手动关闭。"
                    android:textColor="#856404"
                    android:textSize="14sp"
                    android:lineSpacingExtra="3dp" />
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout> 