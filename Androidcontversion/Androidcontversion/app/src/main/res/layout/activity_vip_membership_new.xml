<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color">

    <!-- 顶部导航栏 -->
    <RelativeLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/primary_color"
        android:elevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/backBtn"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_centerVertical="true"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="12dp"
            android:src="@drawable/ic_back" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="会员中心"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />
    </RelativeLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="24dp">

            <!-- 顶部会员宣传 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="180dp"
                android:layout_margin="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/bg_gradient_blue">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="24dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="专业数据恢复工具"
                            android:textColor="@color/white"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="VIP尊享会员"
                            android:textColor="@color/white"
                            android:textSize="24sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="解锁所有功能，恢复珍贵回忆"
                            android:textColor="@color/white"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:background="@drawable/bg_badge_premium"
                            android:paddingHorizontal="12dp"
                            android:paddingVertical="4dp"
                            android:text="优质服务"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <ImageView
                        android:layout_width="120dp"
                        android:layout_height="120dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="16dp"
                        android:alpha="0.7"
                        android:src="@drawable/ic_data_recovery" />
                </RelativeLayout>
            </androidx.cardview.widget.CardView>

            <!-- 会员推荐 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:text="为您推荐"
                android:textColor="@color/text_color"
                android:textSize="18sp"
                android:textStyle="bold" />

            <!-- 高级会员 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/permanent_package"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginVertical="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_gradient_orange"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="推荐套餐"
                            android:textColor="@color/white"
                            android:textSize="12sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:text="节省50%"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            android:textStyle="bold" />
                    </RelativeLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/permanent_title"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="高级VIP会员"
                                android:textColor="@color/text_color"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <ImageView
                                android:id="@+id/check_permanent"
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:src="@drawable/ic_check_circle"
                                android:visibility="visible"
                                app:tint="@color/orange" />
                        </RelativeLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="一次付款，终身使用全部高级功能"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/permanent_price"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="¥159"
                                android:textColor="@color/orange"
                                android:textSize="24sp"
                                android:textStyle="bold" />


                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 年度会员 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/annual_package"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginVertical="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/annual_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="年度VIP会员"
                            android:textColor="@color/text_color"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <ImageView
                            android:id="@+id/check_annual"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:src="@drawable/ic_check_circle"
                            android:visibility="invisible"
                            app:tint="@color/blue_primary" />
                    </RelativeLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="一年内无限制使用全部高级功能"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/annual_price"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="¥79"
                            android:textColor="@color/blue_primary"
                            android:textSize="24sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:background="@drawable/strike_through"
                            android:text="¥158"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 会员特权 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="24dp"
                android:layout_marginBottom="8dp"
                android:text="会员特权"
                android:textColor="@color/text_color"
                android:textSize="18sp"
                android:textStyle="bold" />

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginVertical="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- 特权项 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginEnd="16dp"
                            android:background="@drawable/bg_check_circle"
                            android:padding="6dp"
                            android:src="@drawable/ic_check"
                            app:tint="@color/white" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="无限数据恢复"
                                android:textColor="@color/text_color"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="不限数量和大小，恢复所有类型的文件"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginEnd="16dp"
                            android:background="@drawable/bg_check_circle"
                            android:padding="6dp"
                            android:src="@drawable/ic_check"
                            app:tint="@color/white" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="聊天记录恢复"
                                android:textColor="@color/text_color"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="微信、QQ、支付宝等重要聊天记录恢复"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginEnd="16dp"
                            android:background="@drawable/bg_check_circle"
                            android:padding="6dp"
                            android:src="@drawable/ic_check"
                            app:tint="@color/white" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="深度扫描恢复"
                                android:textColor="@color/text_color"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="使用高级算法深度扫描，提高恢复成功率"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginEnd="16dp"
                            android:background="@drawable/bg_check_circle"
                            android:padding="6dp"
                            android:src="@drawable/ic_check"
                            app:tint="@color/white" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="专属客服支持"
                                android:textColor="@color/text_color"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="专业技术团队一对一远程协助"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 用户评价 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="24dp"
                android:layout_marginBottom="8dp"
                android:text="用户评价"
                android:textColor="@color/text_color"
                android:textSize="18sp"
                android:textStyle="bold" />

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginVertical="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- 评价项 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginEnd="12dp"
                            android:background="@drawable/bg_avatar"
                            android:padding="8dp"
                            android:src="@drawable/ic_profile"
                            app:tint="@color/white" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="李先生"
                                android:textColor="@color/text_color"
                                android:textSize="14sp"
                                android:textStyle="bold" />

                            <RatingBar
                                style="?android:attr/ratingBarStyleSmall"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:isIndicator="true"
                                android:numStars="5"
                                android:rating="5"
                                android:stepSize="0.1"
                                android:theme="@style/RatingBar" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="太好用了，我的微信聊天记录误删了，用这个软件全部恢复回来了，非常感谢！"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginEnd="12dp"
                            android:background="@drawable/bg_avatar"
                            android:padding="8dp"
                            android:src="@drawable/ic_profile"
                            app:tint="@color/white" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="王女士"
                                android:textColor="@color/text_color"
                                android:textSize="14sp"
                                android:textStyle="bold" />

                            <RatingBar
                                style="?android:attr/ratingBarStyleSmall"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:isIndicator="true"
                                android:numStars="5"
                                android:rating="5"
                                android:stepSize="0.1"
                                android:theme="@style/RatingBar" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="手机里的重要照片删了，试了很多软件都不行，这个软件恢复了95%以上，很满意！"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 底部按钮和协议 -->
            <Button
                android:id="@+id/btn_activate_vip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="24dp"
                android:background="@drawable/bg_button_gradient"
                android:padding="16dp"
                android:text="立即开通会员"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/terms_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center"
                android:text="点击开通即表示同意《服务协议》和《隐私政策》"
                android:textColor="@color/text_secondary"
                android:textSize="12sp" />
        </LinearLayout>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout> 