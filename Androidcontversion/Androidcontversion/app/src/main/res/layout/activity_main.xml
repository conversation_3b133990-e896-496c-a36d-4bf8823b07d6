<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <!-- 全屏滚动视图 -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/bottom_nav">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!-- Blue Background -->
            <View
                android:id="@+id/blue_background"
                android:layout_width="0dp"
                android:layout_height="280dp"
                android:background="@drawable/blue_background"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Status Bar -->
            <LinearLayout
                android:id="@+id/status_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:layout_marginEnd="15dp"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent">
                
                <!-- WiFi图标已删除 -->
            </LinearLayout>

            <!-- Header - 已删除手机修复标题 -->
            <TextView
                android:id="@+id/header_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="20dp"
                android:text="@string/data_recovery"
                android:textColor="@color/black"
                android:textSize="32sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/status_bar" />

            <!-- Recovery Box -->
            <androidx.cardview.widget.CardView
                android:id="@+id/recovery_box"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:layout_marginTop="15dp"
                android:layout_marginEnd="15dp"
                app:cardBackgroundColor="@color/blue_primary"
                app:cardCornerRadius="15dp"
                app:cardElevation="5dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/header_title">

                <!-- 恢复框内容 -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="20dp">

                    <LinearLayout
                        android:id="@+id/recovery_content"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/file_image"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/early_fix_line1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/recover_early"
                            android:textColor="@color/white"
                            android:textSize="26sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/early_fix_line2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/higher_chance"
                            android:textColor="@color/white"
                            android:textSize="26sp"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:id="@+id/safety_features"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/safe_efficient"
                                android:textColor="@color/white"
                                android:textSize="12sp" />

                            <View
                                android:layout_width="1dp"
                                android:layout_height="12dp"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="5dp"
                                android:layout_marginEnd="5dp"
                                android:background="@color/white" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/privacy_assured"
                                android:textColor="@color/white"
                                android:textSize="12sp" />
                        </LinearLayout>

                        <!-- 删除扫描按钮 -->
                    </LinearLayout>

                    <ImageView
                        android:id="@+id/file_image"
                        android:layout_width="100dp"
                        android:layout_height="100dp"
                        android:scaleType="centerInside"
                        android:src="@drawable/ic_file_image"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.cardview.widget.CardView>

            <!-- Notification - 替换为滚动资讯 -->
            <include
                android:id="@+id/news_ticker"
                layout="@layout/news_ticker_layout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:layout_marginTop="15dp"
                android:layout_marginEnd="15dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/recovery_box" />

            <!-- Features Section -->
            <LinearLayout
                android:id="@+id/features"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp"
                app:layout_constraintTop_toBottomOf="@id/news_ticker">

                <TextView
                    android:id="@+id/features_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="15dp"
                    android:text="@string/popular_features"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/features_grid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false" />
            </LinearLayout>

            <!-- Other Services Section -->
            <LinearLayout
                android:id="@+id/other_services"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp"
                app:layout_constraintTop_toBottomOf="@id/features">

                <TextView
                    android:id="@+id/other_services_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="15dp"
                    android:text="@string/other_services"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/other_services_grid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <!-- Bottom Navigation -->
    <LinearLayout
        android:id="@+id/bottom_nav"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="horizontal"
        android:padding="15dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <LinearLayout
            android:id="@+id/nav_home"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/nav_home_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_home" />

            <TextView
                android:id="@+id/nav_home_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tab_home"
                android:textColor="@color/blue_primary"
                android:textSize="12sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/nav_cases"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/nav_cases_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_cases" />

            <TextView
                android:id="@+id/nav_cases_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/cases"
                android:textColor="@color/text_secondary"
                android:textSize="12sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/nav_order"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/nav_order_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_order" />

            <TextView
                android:id="@+id/nav_order_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/order"
                android:textColor="@color/text_secondary"
                android:textSize="12sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/nav_profile"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/nav_profile_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_profile" />

            <TextView
                android:id="@+id/nav_profile_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/profile"
                android:textColor="@color/text_secondary"
                android:textSize="12sp" />
        </LinearLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout> 