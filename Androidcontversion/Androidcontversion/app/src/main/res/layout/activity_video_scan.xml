<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f5f5f5"
    tools:context=".VideoScanActivity">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- 顶部导航栏 -->
        <RelativeLayout
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#f5f5f5"
            android:elevation="2dp"
            android:padding="15dp">

            <ImageButton
                android:id="@+id/backBtn"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/back"
                android:padding="8dp"
                android:src="@drawable/ic_arrow_back"
                app:tint="@color/text_primary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/all_videos"
                android:textColor="@color/text_primary"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/menuBtn"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:gravity="center"
                android:text="≡"
                android:textColor="@color/text_primary"
                android:textSize="24sp" />
        </RelativeLayout>

        <!-- 筛选选项 -->
        <HorizontalScrollView
            android:id="@+id/filters"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/header"
            android:background="#f5f5f5"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingHorizontal="40dp"
                android:paddingVertical="12dp">

                <TextView
                    android:id="@+id/filterTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/bg_filter_selected"
                    android:paddingHorizontal="15dp"
                    android:paddingVertical="5dp"
                    android:text="@string/time"
                    android:textColor="@color/blue_primary"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/filterSize"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="15dp"
                    android:paddingVertical="5dp"
                    android:text="@string/size"
                    android:textColor="@color/text_secondary"
                    android:textSize="15sp" />
            </LinearLayout>
        </HorizontalScrollView>

        <!-- 扫描进度条 -->
        <RelativeLayout
            android:id="@+id/scanProgressBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/filters"
            android:layout_marginBottom="15dp"
            android:background="@color/warning_light"
            android:padding="16dp"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toStartOf="@id/recoverBtn"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="10dp"
                        android:background="@drawable/bg_progress_icon"
                        android:gravity="center"
                        android:text="✓"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/progressText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/scanned_percent"
                        android:textColor="@color/warning_text"
                        android:textSize="16sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="34dp"
                    android:layout_marginTop="8dp"
                    android:text="@string/click_recover_deep_scan"
                    android:textColor="@color/warning_text"
                    android:textSize="14sp" />
            </LinearLayout>

            <Button
                android:id="@+id/recoverBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:background="@drawable/bg_button_warning"
                android:paddingHorizontal="16dp"
                android:paddingVertical="8dp"
                android:text="@string/recover_immediately"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:textStyle="bold" />
        </RelativeLayout>

        <!-- 视频列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/videoList"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/scanProgressBar"
            android:layout_marginBottom="80dp"
            android:clipToPadding="false"
            android:padding="15dp" />

        <!-- 底部按钮 -->
        <Button
            android:id="@+id/bottomActionBtn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginHorizontal="30dp"
            android:layout_marginVertical="20dp"
            android:background="@drawable/bg_button_gradient"
            android:drawableStart="@drawable/ic_search"
            android:drawablePadding="8dp"
            android:paddingStart="20dp"
            android:paddingEnd="30dp"
            android:text="@string/open_deep_scan_find_all"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            android:visibility="gone" />
    </RelativeLayout>

    <!-- 扫描中界面 -->
    <RelativeLayout
        android:id="@+id/scanningScreen"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#f5f5f5"
        android:gravity="center"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="20dp">

            <RelativeLayout
                android:layout_width="140dp"
                android:layout_height="140dp"
                android:layout_marginBottom="40dp">

                <View
                    android:id="@+id/outerCircle"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/bg_scan_circle_outer" />

                <View
                    android:id="@+id/middleCircle"
                    android:layout_width="110dp"
                    android:layout_height="110dp"
                    android:layout_centerInParent="true"
                    android:background="@drawable/bg_scan_circle_middle" />

                <View
                    android:id="@+id/innerCircle"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_centerInParent="true"
                    android:background="@drawable/bg_scan_circle_inner" />

                <TextView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:text="🎬"
                    android:textColor="@color/blue_primary"
                    android:textSize="24sp" />
            </RelativeLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="15dp"
                android:text="@string/scanning_video_files"
                android:textColor="@color/text_primary"
                android:textSize="22sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="30dp"
                android:gravity="center"
                android:maxWidth="280dp"
                android:text="@string/scanning_video_desc"
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />

            <ProgressBar
                android:id="@+id/scanProgressBar2"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="300dp"
                android:layout_height="8dp"
                android:layout_marginBottom="10dp"
                android:progress="0"
                android:progressDrawable="@drawable/progress_bar_gradient" />

            <TextView
                android:id="@+id/scanPercentage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="30dp"
                android:text="0%"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/scanFiles"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:alpha="0"
                android:text="@string/files_found"
                android:textColor="@color/text_tertiary"
                android:textSize="14sp" />
        </LinearLayout>
    </RelativeLayout>

    <!-- 退出确认弹窗 - 仅在代码中动态创建 -->

</androidx.constraintlayout.widget.ConstraintLayout> 