<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    tools:context=".FeedbackActivity">

    <!-- Header -->
    <LinearLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary_color"
        android:elevation="2dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="15dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/back"
            android:src="@drawable/ic_back"
            app:tint="@color/white" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/feedback_title"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <View
            android:layout_width="30dp"
            android:layout_height="30dp" />

    </LinearLayout>

    <!-- Divider -->
    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:background="@color/background_color"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header" />

    <!-- Main Content -->
    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Content Container -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="vertical"
                android:padding="20dp">

                <!-- Contact Info Section -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="30dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="15dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="*"
                            android:textColor="#e53935"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="5dp"
                            android:text="@string/contact_info"
                            android:textColor="@color/text_color"
                            android:textSize="16sp" />
                    </LinearLayout>

                    <EditText
                        android:id="@+id/et_contact_info"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_feedback_input"
                        android:hint="@string/feedback_contact_hint"
                        android:importantForAutofill="no"
                        android:inputType="text"
                        android:padding="15dp"
                        android:textColor="@color/text_color"
                        android:textColorHint="@color/text_muted"
                        android:textSize="16sp" />
                </LinearLayout>

                <!-- Issue Description Section -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="15dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="*"
                            android:textColor="#e53935"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="5dp"
                            android:text="@string/issue_description"
                            android:textColor="@color/text_color"
                            android:textSize="16sp" />
                    </LinearLayout>

                    <EditText
                        android:id="@+id/et_issue_description"
                        android:layout_width="match_parent"
                        android:layout_height="150dp"
                        android:background="@drawable/bg_feedback_input"
                        android:gravity="top"
                        android:hint="@string/feedback_issue_hint"
                        android:importantForAutofill="no"
                        android:inputType="textMultiLine"
                        android:padding="15dp"
                        android:textColor="@color/text_color"
                        android:textColorHint="@color/text_muted"
                        android:textSize="16sp" />
                </LinearLayout>

            </LinearLayout>

            <!-- Submit Button -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="20dp">

                <Button
                    android:id="@+id/btn_submit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_feedback_submit_btn"
                    android:paddingTop="15dp"
                    android:paddingBottom="15dp"
                    android:text="@string/submit_feedback"
                    android:textColor="@android:color/white"
                    android:textSize="18sp"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout> 