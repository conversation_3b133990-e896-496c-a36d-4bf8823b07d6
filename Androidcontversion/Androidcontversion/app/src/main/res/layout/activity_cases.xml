<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".CasesActivity">

    <!-- 状态栏 -->
    <LinearLayout
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary_color"
        android:padding="10dp"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 返回按钮已删除 -->
        <View
            android:layout_width="24dp"
            android:layout_height="24dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="精选案例"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginStart="16dp" />

        <View
            android:layout_width="24dp"
            android:layout_height="24dp" />
    </LinearLayout>

    <!-- 案例列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/cases_recycler_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:paddingTop="16dp"
        app:layout_constraintBottom_toTopOf="@id/bottom_nav"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/statusBar" />

    <!-- 底部导航 -->
    <LinearLayout
        android:id="@+id/bottom_nav"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="horizontal"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:elevation="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <LinearLayout
            android:id="@+id/nav_home"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginBottom="5dp"
                android:src="@drawable/ic_home"
                app:tint="@color/text_secondary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="首页"
                android:textColor="@color/text_secondary"
                android:textSize="12sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/nav_cases"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginBottom="5dp"
                android:src="@drawable/ic_clipboard"
                app:tint="@color/blue_primary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="案例"
                android:textColor="@color/blue_primary"
                android:textSize="12sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/nav_order"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginBottom="5dp"
                android:src="@drawable/ic_file"
                app:tint="@color/text_secondary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="订单"
                android:textColor="@color/text_secondary"
                android:textSize="12sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/nav_profile"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginBottom="5dp"
                android:src="@drawable/ic_user"
                app:tint="@color/text_secondary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="我的"
                android:textColor="@color/text_secondary"
                android:textSize="12sp" />
        </LinearLayout>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 