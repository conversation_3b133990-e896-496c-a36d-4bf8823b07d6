<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    tools:context=".ProfileActivity">

    <!-- Header -->
    <LinearLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary_color"
        android:elevation="2dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="15dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 删除返回按钮，用空白View占位 -->
        <View
            android:layout_width="30dp"
            android:layout_height="30dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/profile"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <View
            android:layout_width="30dp"
            android:layout_height="30dp" />

    </LinearLayout>

    <!-- Divider -->
    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:background="@color/background_color"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header" />

    <!-- Main Content -->
    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toTopOf="@id/bottom_navigation" 
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Profile Header -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
            android:orientation="vertical"
                    android:padding="20dp">

                    <androidx.cardview.widget.CardView
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:layout_marginBottom="16dp"
                        app:cardCornerRadius="40dp"
                        app:cardElevation="2dp">

                        <ImageView
                            android:id="@+id/img_avatar"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:contentDescription="@string/profile_avatar"
                            android:scaleType="centerCrop"
                            android:src="@drawable/ic_profile" />
                    </androidx.cardview.widget.CardView>

            <TextView
                        android:id="@+id/tv_username"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                        android:text="@string/default_username"
                        android:textColor="@color/text_color"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                        android:id="@+id/tv_user_id"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="@string/user_id_prefix"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />

                    <!-- 移除编辑资料按钮
                    <Button
                        android:id="@+id/btn_edit_profile"
                android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:layout_marginTop="16dp"
                        android:background="@drawable/bg_contact_action"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:text="@string/edit_profile"
                        android:textColor="@color/contact_primary" />
                    -->
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Personal Information Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="@string/personal_information"
                        android:textColor="@color/text_color"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <!-- Phone Number -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="@string/phone"
                            android:src="@drawable/ic_phone"
                            app:tint="@color/text_secondary" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/phone"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_phone"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:text="@string/default_phone"
                            android:textColor="@color/text_color"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <!-- Email -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="@string/email"
                            android:src="@drawable/ic_email"
                            app:tint="@color/text_secondary" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/email"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_email"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:text="@string/default_email"
                            android:textColor="@color/text_color"
                            android:textSize="14sp" />

    </LinearLayout>

                    <!-- Location -->
    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="@string/location"
                            android:src="@drawable/ic_location"
                            app:tint="@color/text_secondary" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/location"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_location"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:text="@string/default_location"
                            android:textColor="@color/text_color"
                            android:textSize="14sp" />

                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Account Settings Section -->
            <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
                android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="@string/account_settings"
                        android:textColor="@color/text_color"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <!-- 隐私政策 -->
        <LinearLayout
                        android:id="@+id/layout_privacy_policy"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center_vertical"
            android:orientation="horizontal"
                        android:paddingVertical="8dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="@string/privacy_policy"
                            android:src="@drawable/ic_shield"
                            app:tint="@color/text_secondary" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                            android:text="@string/privacy_policy"
                            android:textColor="@color/text_color"
                            android:textSize="14sp" />

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                            android:contentDescription="@string/arrow_right"
                            android:src="@drawable/ic_right"
                            app:tint="@color/text_secondary" />
        </LinearLayout>

                    <!-- 服务协议 -->
        <LinearLayout
                        android:id="@+id/layout_service_agreement"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center_vertical"
            android:orientation="horizontal"
                        android:paddingVertical="8dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="@string/service_agreement_description"
                            android:src="@drawable/ic_info"
                            app:tint="@color/text_secondary" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                            android:text="@string/service_agreement_description"
                            android:textColor="@color/text_color"
                            android:textSize="14sp" />

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                            android:contentDescription="@string/arrow_right"
                            android:src="@drawable/ic_right"
                            app:tint="@color/text_secondary" />
                    </LinearLayout>
        </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- App Settings Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
            android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="@string/app_settings"
                        android:textColor="@color/text_color"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <!-- Language -->
        <LinearLayout
                        android:id="@+id/layout_language"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center_vertical"
            android:orientation="horizontal"
                        android:paddingVertical="8dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="@string/language"
                            android:src="@drawable/ic_language"
                            app:tint="@color/text_secondary" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                            android:text="@string/language"
                            android:textColor="@color/text_color"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_current_language"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="8dp"
                            android:text="@string/default_language"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp" />

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                            android:contentDescription="@string/arrow_right"
                            android:src="@drawable/ic_right"
                            app:tint="@color/text_secondary" />
        </LinearLayout>

                    <!-- Feedback -->
        <LinearLayout
                        android:id="@+id/layout_feedback"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center_vertical"
            android:orientation="horizontal"
                        android:paddingVertical="8dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="@string/feedback"
                            android:src="@drawable/ic_feedback"
                            app:tint="@color/text_secondary" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                            android:text="@string/feedback"
                            android:textColor="@color/text_color"
                            android:textSize="14sp" />

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                            android:contentDescription="@string/arrow_right"
                            android:src="@drawable/ic_right"
                            app:tint="@color/text_secondary" />
        </LinearLayout>

                    <!-- Contact Service -->
        <LinearLayout
                        android:id="@+id/layout_contact_service"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center_vertical"
            android:orientation="horizontal"
                        android:paddingVertical="8dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="@string/contact_us"
                            android:src="@drawable/ic_headset"
                            app:tint="@color/text_secondary" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                            android:text="@string/contact_us"
                            android:textColor="@color/text_color"
                            android:textSize="14sp" />

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                            android:contentDescription="@string/arrow_right"
                            android:src="@drawable/ic_right"
                            app:tint="@color/text_secondary" />
        </LinearLayout>

                    <!-- About Us -->
        <LinearLayout
                        android:id="@+id/layout_about_us"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center_vertical"
            android:orientation="horizontal"
                        android:paddingVertical="8dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                            android:layout_marginEnd="12dp"
                            android:contentDescription="@string/about_us"
                            android:src="@drawable/ic_info"
                            app:tint="@color/text_secondary" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                            android:text="@string/about_us"
                            android:textColor="@color/text_color"
                            android:textSize="14sp" />

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                            android:contentDescription="@string/arrow_right"
                            android:src="@drawable/ic_right"
                            app:tint="@color/text_secondary" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 返回主页 Button -->
            <Button
                android:id="@+id/btn_back_to_home"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                android:background="@drawable/bg_gradient_button"
                android:padding="14dp"
                android:text="@string/back_to_main"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold" />
        </LinearLayout>
    </ScrollView>

    <!-- 添加底部导航栏 -->
    <LinearLayout
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:elevation="8dp"
        android:orientation="horizontal"
        android:padding="15dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <LinearLayout
            android:id="@+id/nav_home"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/nav_home_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_home" />

            <TextView
                android:id="@+id/nav_home_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tab_home"
                android:textColor="@color/text_secondary"
                android:textSize="12sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/nav_cases"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/nav_cases_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_cases" />

            <TextView
                android:id="@+id/nav_cases_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/cases"
                android:textColor="@color/text_secondary"
                android:textSize="12sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/nav_order"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/nav_order_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_order" />

            <TextView
                android:id="@+id/nav_order_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/order"
                android:textColor="@color/text_secondary"
                android:textSize="12sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/nav_profile"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/nav_profile_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_profile_selected" />

            <TextView
                android:id="@+id/nav_profile_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/profile"
                android:textColor="@color/primary_color"
                android:textSize="12sp" />
        </LinearLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout> 