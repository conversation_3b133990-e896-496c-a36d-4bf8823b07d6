<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_color"
    tools:context=".OrderActivity">

    <!-- 状态栏 -->
    <LinearLayout
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary_color"
        android:padding="10dp"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/back_btn"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:onClick="onBackPressed"
            android:src="@drawable/ic_back"
            android:tint="@color/white" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/my_orders"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="center" />

        <View
            android:layout_width="24dp"
            android:layout_height="24dp" />
    </LinearLayout>

    <!-- 最近购买通知 -->
    <TextView
        android:id="@+id/recent_purchase"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_recent_purchase"
        android:padding="8dp"
        android:layout_marginTop="10dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:text="👤 150****346 购买了图片清除(高级版) 3分钟前"
        app:layout_constraintTop_toBottomOf="@id/statusBar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 订单内容区域 -->
    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="15dp"
        android:background="@drawable/bg_rounded_top_corner"
        app:layout_constraintTop_toBottomOf="@id/recent_purchase"
        app:layout_constraintBottom_toTopOf="@id/bottom_navigation"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="20dp">

            <!-- 套餐选择 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <View
                        android:layout_width="4dp"
                        android:layout_height="16dp"
                        android:background="@color/primary_color"
                        android:layout_marginEnd="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="选择开通服务"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold" />
                </LinearLayout>

                <!-- 高级图片清除套餐 -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/package_permanent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="2dp">

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/badge_best_value"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="超值爆款"
                            android:textColor="@color/white"
                            android:background="@drawable/bg_badge_premium"
                            android:paddingStart="10dp"
                            android:paddingEnd="10dp"
                            android:paddingTop="4dp"
                            android:paddingBottom="4dp"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:layout_gravity="top|start"
                            android:layout_marginStart="-5dp"
                            android:layout_marginTop="-5dp"
                            android:rotation="-10" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:padding="15dp">

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="图片清除(高级版)"
                                    android:textColor="@color/primary_color"
                                    android:textSize="18sp"
                                    android:textStyle="bold"
                                    android:layout_marginTop="5dp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="该服务可长期无上限清除相册、微信、QQ图片清除"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="14sp"
                                    android:layout_marginTop="5dp" />

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal"
                                    android:gravity="center_vertical"
                                    android:layout_marginTop="10dp">

                                    <ImageView
                                        android:layout_width="16dp"
                                        android:layout_height="16dp"
                                        android:src="@drawable/ic_clock"
                                        android:tint="@color/error_color" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="@string/permanent_valid"
                                        android:textColor="@color/error_color"
                                        android:textSize="14sp"
                                        android:textStyle="bold"
                                        android:layout_marginStart="5dp" />
                                </LinearLayout>
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:gravity="end">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="¥158"
                                    android:textColor="@color/primary_color"
                                    android:textSize="24sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="¥198"
                                    android:textColor="@color/text_tertiary"
                                    android:textSize="14sp"
                                    android:textStyle="normal"
                                    android:background="@drawable/strike_through" />
                            </LinearLayout>

                            <ImageView
                                android:id="@+id/check_permanent"
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:background="@drawable/bg_check_circle"
                                android:src="@drawable/ic_check"
                                android:padding="6dp"
                                android:layout_gravity="bottom|end"
                                android:layout_marginStart="10dp"
                                android:visibility="visible" />
                        </LinearLayout>
                    </FrameLayout>
                </androidx.cardview.widget.CardView>

                <!-- 一年图片清除套餐 -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/package_annual"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="15dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="图片清除(一年)"
                                android:textColor="@color/primary_color"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="该服务在一年内可以无上限清除相册、微信、QQ图片清除"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp"
                                android:layout_marginTop="5dp" />

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:layout_marginTop="10dp">

                                <ImageView
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    android:src="@drawable/ic_clock"
                                    android:tint="@color/error_color" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="1年有效"
                                    android:textColor="@color/error_color"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:layout_marginStart="5dp" />
                            </LinearLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="end">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="¥78"
                                android:textColor="@color/primary_color"
                                android:textSize="24sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="¥98"
                                android:textColor="@color/text_tertiary"
                                android:textSize="14sp"
                                android:textStyle="normal"
                                android:background="@drawable/strike_through" />
                        </LinearLayout>

                        <ImageView
                            android:id="@+id/check_annual"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:background="@drawable/bg_check_circle"
                            android:src="@drawable/ic_check"
                            android:padding="6dp"
                            android:layout_gravity="bottom|end"
                            android:layout_marginStart="10dp"
                            android:visibility="gone" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>
            </LinearLayout>

            <!-- 使用场景 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <View
                        android:layout_width="4dp"
                        android:layout_height="16dp"
                        android:background="@color/primary_color"
                        android:layout_marginEnd="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="使用场景"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal">

                    <HorizontalScrollView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:scrollbars="none">
                        
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">
                            
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="手机图片彻底删除"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp"
                                android:background="@drawable/bg_scenario_item"
                                android:padding="8dp"
                                android:layout_margin="5dp" />
        
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="选择清除手机图片"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp"
                                android:background="@drawable/bg_scenario_item"
                                android:padding="8dp"
                                android:layout_margin="5dp" />
        
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="扫描手机图片"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp"
                                android:background="@drawable/bg_scenario_item"
                                android:padding="8dp"
                                android:layout_margin="5dp" />
                        </LinearLayout>
                    </HorizontalScrollView>
                </LinearLayout>
            </LinearLayout>

            <!-- 用户评价 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/bg_reviews_section"
                android:layout_margin="20dp"
                android:padding="20dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <View
                        android:layout_width="4dp"
                        android:layout_height="16dp"
                        android:background="@color/primary_color"
                        android:layout_marginEnd="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="优质用户评价"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold" />
                </LinearLayout>

                <!-- 用户评价项 -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="15dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:gravity="center"
                                android:text="冬"
                                android:textSize="18sp"
                                android:textColor="@color/white"
                                android:background="@drawable/bg_avatar"
                                android:layout_marginEnd="10dp" />

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="冬季温暖优雅"
                                    android:textColor="@color/text_primary"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="⭐ 5分"
                                    android:textColor="@color/rating_color"
                                    android:textSize="12sp"
                                    android:layout_marginTop="2dp" />
                            </LinearLayout>
                        </LinearLayout>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="我非常满意数据修复服务的效果。他们专业的团队帮助我成功修复了丢失的文件，让我感到非常安心和放心。他们高效的工作速度和专业的技术水平让我对他们的服务印象深刻。强烈推荐！"
                            android:textColor="@color/text_secondary"
                            android:textSize="13sp"
                            android:layout_marginTop="10dp"
                            android:lineSpacingMultiplier="1.2" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="15dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:gravity="center"
                                android:text="S"
                                android:textSize="18sp"
                                android:textColor="@color/white"
                                android:background="@drawable/bg_avatar"
                                android:layout_marginEnd="10dp" />

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Starlight99"
                                    android:textColor="@color/text_primary"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="⭐ 5分"
                                    android:textColor="@color/rating_color"
                                    android:textSize="12sp"
                                    android:layout_marginTop="2dp" />
                            </LinearLayout>
                        </LinearLayout>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="我遇到了数据丢失的问题，但幸运的是找到了文件数据修复大师。他们的技术团队非常专业，通过他们的努力，我成功地修复了关键文件。"
                            android:textColor="@color/text_secondary"
                            android:textSize="13sp"
                            android:layout_marginTop="10dp"
                            android:lineSpacingMultiplier="1.2" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="随机挑选2条优质评论"
                    android:textColor="@color/text_tertiary"
                    android:textSize="12sp"
                    android:gravity="center"
                    android:layout_marginTop="15dp" />
            </LinearLayout>
        </LinearLayout>
    </ScrollView>

    <!-- 底部操作栏 -->
    <LinearLayout
        android:id="@+id/bottom_action"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="15dp"
        android:background="@color/white"
        android:elevation="8dp"
        app:layout_constraintBottom_toTopOf="@id/bottom_navigation">

        <Button
            android:id="@+id/btn_pay_now"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="立即购买"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:background="@drawable/bg_button_pay"
            android:padding="15dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="支付即代表您已阅读并同意《服务说明》"
            android:textSize="12sp"
            android:textColor="@color/text_secondary"
            android:gravity="center"
            android:layout_marginTop="10dp" />
    </LinearLayout>

    <!-- 底部导航栏 -->
    <LinearLayout
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:background="@color/white"
        android:orientation="horizontal"
        android:elevation="8dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- 首页 -->
        <LinearLayout
            android:id="@+id/nav_home"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:id="@+id/nav_home_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:scaleType="centerInside"
                android:adjustViewBounds="true"
                android:src="@drawable/ic_home" />

            <TextView
                android:id="@+id/nav_home_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tab_home"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="2dp" />
        </LinearLayout>

        <!-- 案例 -->
        <LinearLayout
            android:id="@+id/nav_cases"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:id="@+id/nav_cases_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:scaleType="centerInside"
                android:adjustViewBounds="true"
                android:src="@drawable/ic_cases" />

            <TextView
                android:id="@+id/nav_cases_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/cases"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="2dp" />
        </LinearLayout>

        <!-- 订单 -->
        <LinearLayout
            android:id="@+id/nav_order"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:background="@drawable/bottom_nav_item_background">

            <ImageView
                android:id="@+id/nav_order_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:scaleType="centerInside"
                android:adjustViewBounds="true"
                android:src="@drawable/ic_order_selected" />

            <TextView
                android:id="@+id/nav_order_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/order"
                android:textSize="12sp"
                android:textColor="@color/primary_color"
                android:layout_marginTop="2dp" />
        </LinearLayout>

        <!-- 个人中心 -->
        <LinearLayout
            android:id="@+id/nav_profile"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:id="@+id/nav_profile_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:scaleType="centerInside"
                android:adjustViewBounds="true"
                android:src="@drawable/ic_profile" />

            <TextView
                android:id="@+id/nav_profile_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/profile"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="2dp" />
        </LinearLayout>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 