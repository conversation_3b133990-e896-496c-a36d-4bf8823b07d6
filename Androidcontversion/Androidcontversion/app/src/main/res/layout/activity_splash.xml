<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".SplashActivity">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_splash_gradient">

        <!-- 装饰性气泡 -->
        <View
            android:id="@+id/bubble1"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_alignParentTop="true"
            android:layout_marginStart="40dp"
            android:layout_marginTop="80dp"
            android:alpha="0.1"
            android:background="@drawable/bg_bubble" />

        <View
            android:id="@+id/bubble2"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:layout_marginEnd="40dp"
            android:layout_marginBottom="160dp"
            android:alpha="0.1"
            android:background="@drawable/bg_bubble" />

        <View
            android:id="@+id/bubble3"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="60dp"
            android:alpha="0.1"
            android:background="@drawable/bg_bubble" />

        <View
            android:id="@+id/bubble4"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="120dp"
            android:layout_marginEnd="50dp"
            android:alpha="0.1"
            android:background="@drawable/bg_bubble" />

        <View
            android:id="@+id/bubble5"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="100dp"
            android:layout_marginBottom="120dp"
            android:alpha="0.1"
            android:background="@drawable/bg_bubble" />

        <!-- 状态栏（模拟） -->
        <TextView
            android:id="@+id/statusBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="15dp"
            android:text="12:30"
            android:textColor="@color/white"
            android:textSize="14sp" />

        <!-- 新的设计：数据修复动态图标 -->
        <FrameLayout
            android:id="@+id/logoContainer"
            android:layout_width="180dp"
            android:layout_height="180dp"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="150dp">
            
            <!-- 背景圆形 -->
            <View
                android:layout_width="180dp"
                android:layout_height="180dp"
                android:background="@drawable/bg_splash_circle"
                android:alpha="0.15" />
                
            <!-- 数据流动画图形 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:gravity="center">
                
                <!-- 数据恢复符号 -->
                <ImageView
                    android:id="@+id/dataIcon"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:src="@drawable/ic_data_recovery"
                    app:tint="@color/white" />
                
                <!-- 动态扫描线 -->
                <View
                    android:id="@+id/scanLine"
                    android:layout_width="100dp"
                    android:layout_height="2dp"
                    android:layout_marginTop="10dp"
                    android:background="@color/white"
                    android:alpha="0.7" />
                    

            </LinearLayout>
        </FrameLayout>

        <!-- 应用名称和口号 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/logoContainer"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="20dp"
            android:text="数据恢复专家"
            android:textColor="@color/white"
            android:textSize="24sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/logoContainer"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="60dp"
            android:text="安全高效 · 数据无忧"
            android:textColor="@color/white"
            android:alpha="0.9"
            android:textSize="14sp" />

        <!-- 加载动画已删除 -->

        <!-- 底部公司信息 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="30dp"
            android:text="© 2025 数据恢复专家 版权所有"
            android:textColor="@color/white"
            android:alpha="0.6"
            android:textSize="12sp" />
    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 