<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    tools:context=".AudioScanActivity">

    <!-- Header -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background_color"
        android:elevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/back"
                android:src="@drawable/ic_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/all_audio"
                android:textColor="@color/text_color"
                android:textSize="18sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageButton
                android:id="@+id/btn_menu"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/menu"
                android:src="@drawable/ic_menu"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.Toolbar>

    <!-- Filters -->
    <LinearLayout
        android:id="@+id/filters_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/background_color"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center"
        android:elevation="2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <TextView
            android:id="@+id/filter_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_filter_active"
            android:paddingStart="15dp"
            android:paddingTop="5dp"
            android:paddingEnd="15dp"
            android:paddingBottom="5dp"
            android:text="@string/time"
            android:textColor="@color/primary_color"
            android:drawableEnd="@drawable/ic_dropdown"
            android:drawablePadding="5dp"
            android:layout_marginEnd="20dp" />

        <TextView
            android:id="@+id/filter_size"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_filter_inactive"
            android:paddingStart="15dp"
            android:paddingTop="5dp"
            android:paddingEnd="15dp"
            android:paddingBottom="5dp"
            android:text="@string/size"
            android:textColor="@color/text_secondary"
            android:drawableEnd="@drawable/ic_dropdown"
            android:drawablePadding="5dp" />

    </LinearLayout>

    <!-- Scan Progress Bar -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_scan_progress"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:visibility="invisible"
        app:cardBackgroundColor="@color/warning_light"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/filters_container">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp">

            <LinearLayout
                android:id="@+id/progress_info_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/btn_recover"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_progress_icon"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="10dp"
                        android:background="@drawable/bg_circle_warning"
                        android:gravity="center"
                        android:text="!"
                        android:textColor="@android:color/white"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_progress_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/scanned_progress"
                        android:textColor="@color/warning_text"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tv_progress_desc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="34dp"
                    android:layout_marginTop="8dp"
                    android:text="@string/scan_deeper_desc"
                    android:textColor="@color/warning_text"
                    android:textSize="14sp" />

            </LinearLayout>

            <Button
                android:id="@+id/btn_recover"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_button_warning"
                android:paddingStart="16dp"
                android:paddingTop="8dp"
                android:paddingEnd="16dp"
                android:paddingBottom="8dp"
                android:text="@string/recover"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

    <!-- Audio Selector -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_audio_selector"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="15dp"
        app:cardBackgroundColor="@android:color/white"
        app:cardCornerRadius="10dp"
        app:cardElevation="2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/filters_container">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="15dp"
                android:text="@string/select_local_audio"
                android:textColor="@color/primary_color"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:text="@string/select_audio_desc"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

            <Button
                android:id="@+id/btn_browse_audio"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_button_primary"
                android:paddingStart="20dp"
                android:paddingTop="10dp"
                android:paddingEnd="20dp"
                android:paddingBottom="10dp"
                android:text="@string/select_audio"
                android:textColor="@android:color/white"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_selected_audios"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:visibility="gone" />

            <Button
                android:id="@+id/btn_start_scan"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:background="@drawable/bg_button_warning"
                android:paddingStart="20dp"
                android:paddingTop="10dp"
                android:paddingEnd="20dp"
                android:paddingBottom="10dp"
                android:text="@string/start_scan"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:visibility="gone" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Audio List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/audio_list"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:clipToPadding="false"
        android:padding="15dp"
        android:paddingBottom="80dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/card_scan_progress"
        tools:listitem="@layout/item_audio" />

    <!-- Bottom Action Button -->
    <Button
        android:id="@+id/btn_bottom_action"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="30dp"
        android:layout_marginEnd="30dp"
        android:layout_marginBottom="30dp"
        android:background="@drawable/bg_button_primary"
        android:drawableStart="@drawable/ic_search"
        android:drawablePadding="8dp"
        android:paddingStart="20dp"
        android:paddingTop="12dp"
        android:paddingEnd="20dp"
        android:paddingBottom="12dp"
        android:text="@string/recover_and_scan_deep"
        android:textColor="@android:color/white"
        android:textSize="16sp"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <!-- Scanning Screen -->
    <FrameLayout
        android:id="@+id/scanning_screen"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/background_color"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="vertical"
            android:gravity="center">

            <FrameLayout
                android:layout_width="140dp"
                android:layout_height="140dp"
                android:layout_marginBottom="40dp">

                <View
                    android:id="@+id/scan_circle_outer"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/bg_scan_circle_outer" />

                <View
                    android:id="@+id/scan_circle_middle"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="15dp"
                    android:background="@drawable/bg_scan_circle_middle" />

                <View
                    android:id="@+id/scan_circle_inner"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="30dp"
                    android:background="@drawable/bg_scan_circle_inner" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="🎵"
                    android:textColor="@color/primary_color"
                    android:textSize="40sp" />

            </FrameLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="15dp"
                android:text="@string/scanning_audio_files"
                android:textColor="@color/text_color"
                android:textSize="22sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="280dp"
                android:layout_height="wrap_content"
                android:layout_marginBottom="30dp"
                android:gravity="center"
                android:lineSpacingExtra="3dp"
                android:text="@string/scanning_audio_desc"
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />

            <ProgressBar
                android:id="@+id/scan_progress_bar"
                style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                android:layout_width="300dp"
                android:layout_height="8dp"
                android:layout_marginBottom="10dp"
                android:progressDrawable="@drawable/progress_bar_gradient" />

            <TextView
                android:id="@+id/tv_scan_percentage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="30dp"
                android:text="0%"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_scan_files"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:alpha="0"
                android:text="@string/found_files"
                android:textColor="@color/text_muted"
                android:textSize="14sp" />

        </LinearLayout>
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 