<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="8dp"
    android:paddingHorizontal="10dp"
    android:background="@drawable/news_ticker_bg">

    <TextView
        android:id="@+id/notice_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/blue_primary"
        android:paddingStart="6dp"
        android:paddingTop="2dp"
        android:paddingEnd="6dp"
        android:paddingBottom="2dp"
        android:text="@string/news"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:textStyle="bold"
        android:layout_centerVertical="true" />

    <ViewFlipper
        android:id="@+id/news_flipper"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_toEndOf="@id/notice_tag"
        android:autoStart="true"
        android:flipInterval="10000"
        android:inAnimation="@anim/slide_in_bottom"
        android:outAnimation="@anim/slide_out_top">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/text_primary"
            android:textSize="14sp"
            android:singleLine="true"
            android:ellipsize="end"
            tools:text="136****4529 刚刚成功恢复了微信数据" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/text_primary"
            android:textSize="14sp"
            android:singleLine="true"
            android:ellipsize="end"
            tools:text="158****1234 10分钟前恢复了相册照片" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/text_primary"
            android:textSize="14sp"
            android:singleLine="true"
            android:ellipsize="end"
            tools:text="177****6485 刚刚购买了图片清除服务" />

    </ViewFlipper>
</RelativeLayout> 