<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/service_card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="5dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="3dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="15dp">

        <ImageView
            android:id="@+id/service_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginBottom="8dp"
            android:src="@drawable/ic_home" />

        <TextView
            android:id="@+id/service_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/file_repair"
            android:textColor="@color/text_primary"
            android:textSize="12sp"
            android:textStyle="bold" />
    </LinearLayout>
</androidx.cardview.widget.CardView> 