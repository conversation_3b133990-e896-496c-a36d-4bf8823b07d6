<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".SettingsActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 头部 -->
        <RelativeLayout
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/blue_primary"
            android:padding="15dp">

            <ImageButton
                android:id="@+id/backBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:background="@null"
                android:contentDescription="@string/back"
                android:src="@drawable/ic_arrow_back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/settings"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold" />
        </RelativeLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 通用设置 -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/light_gray"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="15dp"
                    android:text="@string/general_settings"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:orientation="vertical">

                    <!-- 通知提醒 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="15dp"
                            android:contentDescription="@string/notification"
                            android:src="@drawable/ic_notification"
                            app:tint="@color/blue_primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/notification_alerts"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/notification_desc"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />
                        </LinearLayout>

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/notificationSwitch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:checked="true" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/divider" />

                    <!-- 深色模式 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="15dp"
                            android:contentDescription="@string/dark_mode"
                            android:src="@drawable/ic_dark_mode"
                            app:tint="@color/blue_primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/dark_mode"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/dark_mode_desc"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />
                        </LinearLayout>

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/darkModeSwitch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/divider" />

                    <!-- 语言 -->
                    <LinearLayout
                        android:id="@+id/languageOption"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="15dp"
                            android:contentDescription="@string/language"
                            android:src="@drawable/ic_language"
                            app:tint="@color/blue_primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/language"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/language_desc"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />
                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="10dp"
                            android:text="@string/simplified_chinese"
                            android:textColor="@color/text_tertiary"
                            android:textSize="14sp" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:contentDescription="@string/arrow"
                            android:src="@drawable/ic_chevron_right" />
                    </LinearLayout>

                </LinearLayout>

                <!-- 账户与安全 -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/light_gray"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="15dp"
                    android:text="@string/account_and_security"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:orientation="vertical">

                    <!-- 账户安全 -->
                    <LinearLayout
                        android:id="@+id/accountSecurityOption"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="15dp"
                            android:contentDescription="@string/account_security"
                            android:src="@drawable/ic_security"
                            app:tint="@color/blue_primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/account_security"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/account_security_desc"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />
                        </LinearLayout>

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:contentDescription="@string/arrow"
                            android:src="@drawable/ic_chevron_right" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/divider" />

                    <!-- 隐私设置 -->
                    <LinearLayout
                        android:id="@+id/privacyOption"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="15dp"
                            android:contentDescription="@string/privacy_settings"
                            android:src="@drawable/ic_lock"
                            app:tint="@color/blue_primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/privacy_settings"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/privacy_settings_desc"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />
                        </LinearLayout>

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:contentDescription="@string/arrow"
                            android:src="@drawable/ic_chevron_right" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/divider" />

                    <!-- 指纹解锁 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="15dp"
                            android:contentDescription="@string/fingerprint_unlock"
                            android:src="@drawable/ic_fingerprint"
                            app:tint="@color/blue_primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/fingerprint_unlock"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/fingerprint_unlock_desc"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />
                        </LinearLayout>

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/fingerprintSwitch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:checked="true" />
                    </LinearLayout>

                </LinearLayout>

                <!-- 数据管理 -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/light_gray"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="15dp"
                    android:text="@string/data_management"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:orientation="vertical">

                    <!-- 存储空间 -->
                    <LinearLayout
                        android:id="@+id/storageOption"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="15dp"
                            android:contentDescription="@string/storage_space"
                            android:src="@drawable/ic_storage"
                            app:tint="@color/blue_primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/storage_space"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/storage_space_desc"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />
                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="10dp"
                            android:text="56.8MB"
                            android:textColor="@color/text_tertiary"
                            android:textSize="14sp" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:contentDescription="@string/arrow"
                            android:src="@drawable/ic_chevron_right" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/divider" />

                    <!-- 自动备份 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="15dp"
                            android:contentDescription="@string/auto_backup"
                            android:src="@drawable/ic_backup"
                            app:tint="@color/blue_primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/auto_backup"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/auto_backup_desc"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />
                        </LinearLayout>

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/backupSwitch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:checked="true" />
                    </LinearLayout>

                </LinearLayout>

                <!-- 关于 -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/light_gray"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="15dp"
                    android:text="@string/about"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:orientation="vertical">

                    <!-- 检查更新 -->
                    <LinearLayout
                        android:id="@+id/checkUpdateOption"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="15dp"
                            android:contentDescription="@string/check_update"
                            android:src="@drawable/ic_update"
                            app:tint="@color/blue_primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/check_update"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/current_version_v1_1_31"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />
                        </LinearLayout>

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:contentDescription="@string/arrow"
                            android:src="@drawable/ic_chevron_right" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/divider" />

                    <!-- 帮助与反馈 -->
                    <LinearLayout
                        android:id="@+id/helpOption"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="15dp"
                            android:contentDescription="@string/help_and_feedback"
                            android:src="@drawable/ic_help"
                            app:tint="@color/blue_primary" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/help_and_feedback"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/help_and_feedback_desc"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />
                        </LinearLayout>

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:contentDescription="@string/arrow"
                            android:src="@drawable/ic_chevron_right" />
                    </LinearLayout>

                </LinearLayout>

                <!-- 清除缓存按钮 -->
                <Button
                    android:id="@+id/clearCacheBtn"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="20dp"
                    android:background="@drawable/bg_button_outline"
                    android:text="@string/clear_cache"
                    android:textColor="@color/blue_primary" />

                <!-- 退出登录按钮 -->
                <Button
                    android:id="@+id/logoutBtn"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="20dp"
                    android:layout_marginBottom="20dp"
                    android:background="@drawable/bg_button_outline_red"
                    android:text="@string/logout"
                    android:textColor="@color/red" />

            </LinearLayout>
        </ScrollView>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 