<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".CaseDetailActivity">

    <!-- 头部 -->
    <LinearLayout
        android:id="@+id/header"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/primary_color"
        android:padding="15dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/back_btn"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="返回"
            android:src="@drawable/ic_back"
            app:tint="@color/white" />

        <TextView
            android:id="@+id/header_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="案例详情"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- 占位，使标题居中 -->
        <View
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:visibility="invisible" />
    </LinearLayout>

    <!-- 内容滚动区域 -->
    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- 案例元数据 -->
            <LinearLayout
                android:id="@+id/case_meta"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_clipboard"
                    app:tint="@color/blue_primary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="精选案例 | "
                    android:textColor="@color/blue_primary"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/case_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/blue_primary"
                    android:textSize="14sp"
                    tools:text="2023-12-24 15:15:32" />
            </LinearLayout>

            <!-- 案例标题 -->
            <TextView
                android:id="@+id/case_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:lineSpacingExtra="4dp"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold"
                tools:text="在手机上使用存储卡来存储大量的照片和视频，不小心，格式化了存储卡，导致里面的所有文件都丢失了" />

            <!-- 案例描述 -->
            <TextView
                android:id="@+id/case_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:lineSpacingExtra="6dp"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                tools:text="明先生是一位手机用户，他在一次意外中不小心格式化了自己的手机存储卡。这个存储卡里保存着他珍贵的照片和视频文件，明先生非常着急，因为这些照片和视频对他来说非常重要。" />

            <!-- 案例图片 -->
            <ImageView
                android:id="@+id/case_image"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layout_marginTop="10dp"
                android:background="#F5F5F5"
                android:scaleType="centerInside"
                android:elevation="2dp" />
        </LinearLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout> 