<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="10dp"
    app:cardElevation="2dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <ImageView
            android:id="@+id/file_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="16dp"
            android:contentDescription="@string/file_icon"
            android:src="@drawable/ic_file_doc" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginEnd="16dp"
            android:layout_toStartOf="@id/remove_button"
            android:layout_toEndOf="@id/file_icon"
            android:orientation="vertical">

            <TextView
                android:id="@+id/file_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="工作报告.doc"
                android:textColor="@color/text_primary"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/file_size"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="15MB"
                android:textColor="@color/text_tertiary"
                android:textSize="14sp" />
        </LinearLayout>

        <ImageButton
            android:id="@+id/remove_button"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="删除"
            android:src="@drawable/ic_close"
            app:tint="@color/text_tertiary" />
    </RelativeLayout>
</androidx.cardview.widget.CardView> 