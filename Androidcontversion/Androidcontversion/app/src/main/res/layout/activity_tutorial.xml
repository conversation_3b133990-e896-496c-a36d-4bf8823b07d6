<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".TutorialActivity">

    <!-- 头部 -->
    <RelativeLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="#5367e4"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <ImageButton
            android:id="@+id/back_btn"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_centerVertical="true"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_back"
            android:contentDescription="返回"
            app:tint="@color/white" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:text="操作教程"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />
    </RelativeLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 教程标题 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="如何使用图片清除功能"
                android:textColor="@color/text_primary"
                android:textSize="22sp"
                android:textStyle="bold"
                android:layout_marginBottom="20dp" />

            <!-- 步骤1 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:background="@drawable/circle_background"
                    android:gravity="center"
                    android:text="1"
                    android:textColor="@color/white"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_gravity="center_vertical"
                    android:text="选择需要清除的图片"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="42dp"
                android:layout_marginBottom="20dp"
                android:text="打开应用后，在主页选择图片清除功能，然后选择您需要清除的图片类型：相册、微信或QQ。系统会自动扫描这些区域的图片。"
                android:textColor="@color/text_secondary"
                android:lineSpacingExtra="4dp" />

            <!-- 步骤2 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:background="@drawable/circle_background"
                    android:gravity="center"
                    android:text="2"
                    android:textColor="@color/white"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_gravity="center_vertical"
                    android:text="确认清除选项"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="42dp"
                android:layout_marginBottom="20dp"
                android:text="扫描完成后，您可以查看所有被发现的图片。勾选您想要清除的图片，然后点击屏幕底部的清除选中按钮。"
                android:textColor="@color/text_secondary"
                android:lineSpacingExtra="4dp" />

            <!-- 步骤3 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:background="@drawable/circle_background"
                    android:gravity="center"
                    android:text="3"
                    android:textColor="@color/white"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_gravity="center_vertical"
                    android:text="完成清除"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="42dp"
                android:layout_marginBottom="30dp"
                android:text="系统会要求您确认删除操作。确认后，系统会彻底清除这些图片，同时清除相关缓存和元数据，确保图片无法恢复。"
                android:textColor="@color/text_secondary"
                android:lineSpacingExtra="4dp" />

            <!-- 提示说明 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="8dp"
                app:cardBackgroundColor="#f1f5ff"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="温馨提示"
                        android:textColor="#5367e4"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="• 清除前请务必确认图片不再需要，因为清除后将无法恢复\n• 建议在清除大量图片前先进行备份\n• 如有任何疑问，请联系客服支持"
                        android:textColor="#666666"
                        android:lineSpacingExtra="6dp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </ScrollView>
</LinearLayout> 