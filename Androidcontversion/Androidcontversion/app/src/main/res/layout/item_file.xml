<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardBackgroundColor="@android:color/white"
    app:cardCornerRadius="10dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="15dp">

        <FrameLayout
            android:id="@+id/file_icon_container"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/bg_file_icon_doc"
            android:padding="8dp">

            <TextView
                android:id="@+id/tv_file_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="W"
                android:textColor="#2196f3"
                android:textSize="18sp"
                android:textStyle="bold" />

        </FrameLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_file_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="项目计划书.docx"
                android:textColor="@color/text_color"
                android:textSize="14sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_file_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="10dp"
                    android:text="昨天"
                    android:textColor="@color/text_muted"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tv_file_size"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="1.2MB"
                    android:textColor="@color/text_muted"
                    android:textSize="12sp" />

            </LinearLayout>

        </LinearLayout>

        <CheckBox
            android:id="@+id/checkbox_file"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_marginStart="10dp"
            android:background="@drawable/selector_file_checkbox"
            android:button="@null"
            android:clickable="true"
            android:focusable="true" />

    </LinearLayout>

</androidx.cardview.widget.CardView> 