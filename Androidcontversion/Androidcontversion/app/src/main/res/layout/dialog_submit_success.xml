<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/white"
    android:padding="20dp">

    <TextView
        android:id="@+id/close_btn"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_alignParentEnd="true"
        android:text="×"
        android:gravity="center"
        android:textSize="24sp"
        android:textColor="#aaaaaa" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="20dp">

        <ImageView
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:src="@drawable/ic_check_circle_large"
            app:tint="@color/success_color"
            android:layout_marginBottom="15dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="提交成功！"
            android:textSize="22sp"
            android:textStyle="bold"
            android:textColor="@color/success_color"
            android:layout_marginBottom="20dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="您的手机号码已成功提交，工程师会尽快与您联系。"
            android:textColor="@color/text_secondary"
            android:textSize="16sp"
            android:gravity="center"
            android:layout_marginBottom="30dp" />

        <Button
            android:id="@+id/return_order_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="返回订单"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:background="@drawable/green_gradient_button_bg"
            android:padding="15dp" />
    </LinearLayout>
</RelativeLayout> 