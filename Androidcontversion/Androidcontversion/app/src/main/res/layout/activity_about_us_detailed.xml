<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    tools:context=".AboutUsDetailedActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Background gradient -->
        <View
            android:id="@+id/header_bg"
            android:layout_width="0dp"
            android:layout_height="180dp"
            android:background="@drawable/bg_header_gradient"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Header -->
        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/back"
            android:src="@drawable/ic_back"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@android:color/white" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="@string/about_us"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Company Logo -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_logo"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_marginTop="30dp"
            app:cardCornerRadius="50dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@android:color/white"
                android:contentDescription="@string/company_logo"
                android:padding="20dp"
                android:src="@drawable/ic_mobile" />
        </androidx.cardview.widget.CardView>

        <!-- Company Info Section -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_company_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="20dp"
            app:cardCornerRadius="15dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/card_logo">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="15dp"
                    android:paddingBottom="10dp"
                    android:text="@string/company_info"
                    android:textColor="#333333"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp">

                    <TextView
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:text="@string/company_name"
                        android:textColor="#888888"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/company_name_value"
                        android:textColor="#333333"
                        android:textSize="14sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#f9f9f9" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp">

                    <TextView
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:text="@string/founded_year"
                        android:textColor="#888888"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/founded_year_value"
                        android:textColor="#333333"
                        android:textSize="14sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#f9f9f9" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp">

                    <TextView
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:text="@string/product_name"
                        android:textColor="#888888"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/product_name_value"
                        android:textColor="#333333"
                        android:textSize="14sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#f9f9f9" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp">

                    <TextView
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:text="@string/current_version"
                        android:textColor="#888888"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/current_version_value"
                        android:textColor="#333333"
                        android:textSize="14sp"
                        android:textStyle="bold" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Company Description -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_company_desc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="20dp"
            app:cardCornerRadius="15dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/card_company_info">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="15dp"
                    android:paddingBottom="10dp"
                    android:text="@string/company_intro"
                    android:textColor="#333333"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="15dp"
                    android:lineSpacingMultiplier="1.6"
                    android:text="@string/company_desc_1"
                    android:textColor="#666666"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:lineSpacingMultiplier="1.6"
                    android:text="@string/company_desc_2"
                    android:textColor="#666666"
                    android:textSize="14sp" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Product Features -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_features"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="20dp"
            app:cardCornerRadius="15dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/card_company_desc">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="15dp"
                    android:paddingBottom="10dp"
                    android:text="@string/product_features"
                    android:textColor="#333333"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:drawablePadding="10dp"
                        android:text="@string/feature_1"
                        android:textColor="#666666"
                        android:textSize="14sp"
                        app:drawableStartCompat="@drawable/ic_bullet" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:drawablePadding="10dp"
                        android:text="@string/feature_2"
                        android:textColor="#666666"
                        android:textSize="14sp"
                        app:drawableStartCompat="@drawable/ic_bullet" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:drawablePadding="10dp"
                        android:text="@string/feature_3"
                        android:textColor="#666666"
                        android:textSize="14sp"
                        app:drawableStartCompat="@drawable/ic_bullet" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:drawablePadding="10dp"
                        android:text="@string/feature_4"
                        android:textColor="#666666"
                        android:textSize="14sp"
                        app:drawableStartCompat="@drawable/ic_bullet" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:drawablePadding="10dp"
                        android:text="@string/feature_5"
                        android:textColor="#666666"
                        android:textSize="14sp"
                        app:drawableStartCompat="@drawable/ic_bullet" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Contact Us -->
        <androidx.cardview.widget.CardView
            android:id="@+id/card_contact"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="20dp"
            app:cardCornerRadius="15dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/card_features">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="15dp"
                    android:paddingBottom="10dp"
                    android:text="@string/contact_us"
                    android:textColor="#333333"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingTop="6dp"
                    android:paddingBottom="6dp">

                    <ImageView
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:background="@drawable/bg_circle_light_blue"
                        android:contentDescription="@string/phone"
                        android:padding="6dp"
                        android:src="@drawable/ic_phone"
                        app:tint="@color/colorPrimary" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:text="@string/customer_phone"
                        android:textColor="#666666"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingTop="6dp"
                    android:paddingBottom="6dp">

                    <ImageView
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:background="@drawable/bg_circle_light_blue"
                        android:contentDescription="@string/qq_item"
                        android:padding="6dp"
                        android:src="@drawable/ic_qq"
                        app:tint="@color/colorPrimary" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:text="@string/customer_qq"
                        android:textColor="#666666"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingTop="6dp"
                    android:paddingBottom="6dp">

                    <ImageView
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:background="@drawable/bg_circle_light_blue"
                        android:contentDescription="@string/email"
                        android:padding="6dp"
                        android:src="@drawable/ic_email"
                        app:tint="@color/colorPrimary" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:text="@string/customer_email"
                        android:textColor="#666666"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingTop="6dp"
                    android:paddingBottom="6dp">

                    <ImageView
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:background="@drawable/bg_circle_light_blue"
                        android:contentDescription="@string/location"
                        android:padding="6dp"
                        android:src="@drawable/ic_location"
                        app:tint="@color/colorPrimary" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:text="@string/customer_address"
                        android:textColor="#666666"
                        android:textSize="14sp" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Footer -->
        <LinearLayout
            android:id="@+id/layout_footer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/card_contact">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="15dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_marginEnd="10dp"
                    android:background="@drawable/bg_circle_light_gray"
                    android:contentDescription="@string/wechat_item"
                    android:padding="8dp"
                    android:src="@drawable/ic_wechat_bill" />

                <ImageView
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_marginEnd="10dp"
                    android:background="@drawable/bg_circle_light_gray"
                    android:contentDescription="@string/tiktok"
                    android:padding="8dp"
                    android:src="@drawable/ic_tiktok" />

                <ImageView
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:background="@drawable/bg_circle_light_gray"
                    android:contentDescription="@string/website"
                    android:padding="8dp"
                    android:src="@drawable/ic_globe" />
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="15dp"
                android:text="@string/copyright"
                android:textColor="#999999"
                android:textSize="12sp" />

            <Button
                android:id="@+id/btn_return"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_button_gradient"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:text="@string/return_to_profile"
                android:textColor="@android:color/white" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView> 