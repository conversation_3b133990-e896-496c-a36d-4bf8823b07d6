<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/feature_card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="5dp"
    app:cardCornerRadius="15dp"
    app:cardElevation="4dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="20dp">

        <TextView
            android:id="@+id/feature_badge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentTop="true"
            android:background="@drawable/badge_premium"
            android:paddingStart="6dp"
            android:paddingEnd="6dp"
            android:paddingTop="2dp"
            android:paddingBottom="2dp"
            android:text="@string/trial"
            android:textColor="@color/white"
            android:textSize="10sp" />

        <ImageView
            android:id="@+id/feature_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="10dp"
            android:src="@drawable/ic_home" />

        <TextView
            android:id="@+id/feature_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/feature_icon"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="5dp"
            android:text="@string/wechat_message_repair"
            android:textColor="@color/text_primary"
            android:textSize="14sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/feature_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/feature_title"
            android:layout_centerHorizontal="true"
            android:text="@string/wechat_message_repair_desc"
            android:textColor="@color/text_secondary"
            android:textSize="12sp" />
    </RelativeLayout>
</androidx.cardview.widget.CardView> 