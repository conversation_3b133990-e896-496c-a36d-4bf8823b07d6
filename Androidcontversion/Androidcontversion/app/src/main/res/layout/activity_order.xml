<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".OrderActivity">

    <!-- 状态栏 -->
    <LinearLayout
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary_color"
        android:padding="10dp"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 返回按钮已删除 -->
        <View
            android:layout_width="24dp"
            android:layout_height="24dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/my_orders"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginStart="16dp" />

        <View
            android:layout_width="24dp"
            android:layout_height="24dp" />
    </LinearLayout>

    <!-- 订单列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/order_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintTop_toBottomOf="@id/statusBar"
        app:layout_constraintBottom_toTopOf="@id/bottom_navigation" />

    <!-- 空订单状态 -->
    <LinearLayout
        android:id="@+id/empty_order_state"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="vertical"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@id/statusBar"
        app:layout_constraintBottom_toTopOf="@id/bottom_navigation"
        android:visibility="gone">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@drawable/ic_clipboard"
            android:alpha="0.5" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/no_orders"
            android:textSize="18sp"
            android:textColor="@color/text_secondary"
            android:layout_marginTop="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/no_order_records"
            android:textSize="14sp"
            android:textColor="@color/text_tertiary"
            android:layout_marginTop="8dp" />
    </LinearLayout>

    <!-- 底部导航栏 -->
    <LinearLayout
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:background="@color/white"
        android:orientation="horizontal"
        android:elevation="8dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- 首页 -->
        <LinearLayout
            android:id="@+id/nav_home"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:id="@+id/nav_home_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:scaleType="centerInside"
                android:adjustViewBounds="true"
                android:src="@drawable/ic_home" />

            <TextView
                android:id="@+id/nav_home_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tab_home"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="2dp" />
        </LinearLayout>

        <!-- 案例 -->
        <LinearLayout
            android:id="@+id/nav_cases"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:id="@+id/nav_cases_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:scaleType="centerInside"
                android:adjustViewBounds="true"
                android:src="@drawable/ic_cases" />

            <TextView
                android:id="@+id/nav_cases_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/cases"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="2dp" />
        </LinearLayout>

        <!-- 订单 -->
        <LinearLayout
            android:id="@+id/nav_order"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:background="@drawable/bottom_nav_item_background">

            <ImageView
                android:id="@+id/nav_order_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:scaleType="centerInside"
                android:adjustViewBounds="true"
                android:src="@drawable/ic_order_selected" />

            <TextView
                android:id="@+id/nav_order_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/order"
                android:textSize="12sp"
                android:textColor="@color/primary_color"
                android:layout_marginTop="2dp" />
        </LinearLayout>

        <!-- 个人中心 -->
        <LinearLayout
            android:id="@+id/nav_profile"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:id="@+id/nav_profile_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:scaleType="centerInside"
                android:adjustViewBounds="true"
                android:src="@drawable/ic_profile" />

            <TextView
                android:id="@+id/nav_profile_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/profile"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:layout_marginTop="2dp" />
        </LinearLayout>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 