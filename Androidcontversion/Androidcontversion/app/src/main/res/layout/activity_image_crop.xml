<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_dark"
    tools:context=".ImageCropActivity">

    <!-- 顶部导航栏 -->
    <RelativeLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background_dark"
        android:elevation="2dp"
        android:padding="15dp"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/back_btn"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/back"
            android:padding="8dp"
            android:src="@drawable/ic_arrow_back"
            app:tint="@color/white" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/image_crop"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />
    </RelativeLayout>

    <!-- 图片预览区域 -->
    <FrameLayout
        android:id="@+id/imageContainer"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="16dp"
        android:background="@color/background_dark"
        app:layout_constraintBottom_toTopOf="@id/buttonContainer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header">

        <ImageView
            android:id="@+id/image_preview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@string/image_preview"
            android:scaleType="centerInside"
            tools:src="@drawable/sample_image" />

        <ImageView
            android:id="@+id/crop_overlay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:background="@drawable/crop_overlay"
            android:contentDescription="裁剪区域" />
    </FrameLayout>

    <!-- 进度条 -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 底部按钮 -->
    <LinearLayout
        android:id="@+id/buttonContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent">

        <Button
            android:id="@+id/select_image_btn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:background="@drawable/bg_button_primary"
            android:drawableStart="@drawable/ic_image"
            android:drawablePadding="8dp"
            android:gravity="center"
            android:padding="12dp"
            android:text="@string/select_image"
            android:textColor="@color/white" />

        <Button
            android:id="@+id/rotate_btn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:background="@drawable/bg_button_outline"
            android:drawableStart="@drawable/ic_rotate"
            android:drawablePadding="8dp"
            android:gravity="center"
            android:padding="12dp"
            android:text="@string/rotate"
            android:textColor="@color/white" />

        <Button
            android:id="@+id/crop_btn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:background="@drawable/bg_button_outline"
            android:drawableStart="@drawable/ic_crop"
            android:drawablePadding="8dp"
            android:gravity="center"
            android:padding="12dp"
            android:text="@string/crop"
            android:textColor="@color/white" />

        <Button
            android:id="@+id/save_btn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/bg_button_outline"
            android:drawableStart="@drawable/ic_save"
            android:drawablePadding="8dp"
            android:gravity="center"
            android:padding="12dp"
            android:text="@string/save"
            android:textColor="@color/white" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 