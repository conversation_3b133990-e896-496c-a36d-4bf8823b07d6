<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color">

    <!-- 顶部导航栏 -->
    <LinearLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary_color"
        android:elevation="2dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="15dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/backBtn"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_back" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="会员中心"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <View
            android:layout_width="30dp"
            android:layout_height="30dp" />
    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 会员宣传横幅 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="150dp"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/blue_primary">

                    <TextView
                        android:id="@+id/banner_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="24dp"
                        android:layout_marginTop="24dp"
                        android:text="尊享VIP会员特权"
                        android:textColor="@color/white"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="解锁全部高级功能，数据恢复无限制"
                        android:textColor="@color/white"
                        android:textSize="14sp"
                        app:layout_constraintStart_toStartOf="@id/banner_title"
                        app:layout_constraintTop_toBottomOf="@id/banner_title" />

                    <ImageView
                        android:layout_width="100dp"
                        android:layout_height="100dp"
                        android:layout_marginEnd="16dp"
                        android:src="@drawable/ic_profile"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:tint="@color/white" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.cardview.widget.CardView>

            <!-- 会员套餐选择 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:text="选择会员套餐"
                android:textColor="@color/text_color"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- 高级套餐 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/permanent_package"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/permanent_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="高级会员"
                        android:textColor="@color/text_color"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="一次付费，长期享受全部功能"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/permanent_title" />

                    <TextView
                        android:id="@+id/permanent_price"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="¥158"
                        android:textColor="@color/blue_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/check_permanent"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="8dp"
                        android:src="@drawable/ic_check_circle"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="@id/permanent_price"
                        app:layout_constraintEnd_toStartOf="@id/permanent_price"
                        app:layout_constraintTop_toTopOf="@id/permanent_price"
                        app:tint="@color/blue_primary" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.cardview.widget.CardView>

            <!-- 年度套餐 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/annual_package"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/annual_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="年度会员"
                        android:textColor="@color/text_color"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="优惠价格，一年内享受全部功能"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/annual_title" />

                    <TextView
                        android:id="@+id/annual_price"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="¥78"
                        android:textColor="@color/blue_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/check_annual"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="8dp"
                        android:src="@drawable/ic_check_circle"
                        android:visibility="invisible"
                        app:layout_constraintBottom_toBottomOf="@id/annual_price"
                        app:layout_constraintEnd_toStartOf="@id/annual_price"
                        app:layout_constraintTop_toTopOf="@id/annual_price"
                        app:tint="@color/blue_primary" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.cardview.widget.CardView>

            <!-- 会员特权列表 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:text="会员特权"
                android:textColor="@color/text_color"
                android:textSize="16sp"
                android:textStyle="bold" />

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- 特权1 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="12dp"
                            android:src="@drawable/ic_check"
                            app:tint="@color/blue_primary" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="无限制恢复照片、视频和文档"
                            android:textColor="@color/text_color"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <!-- 特权2 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="12dp"
                            android:src="@drawable/ic_check"
                            app:tint="@color/blue_primary" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="微信聊天记录和朋友圈恢复"
                            android:textColor="@color/text_color"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <!-- 特权3 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="12dp"
                            android:src="@drawable/ic_check"
                            app:tint="@color/blue_primary" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="QQ和支付宝数据恢复"
                            android:textColor="@color/text_color"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <!-- 特权4 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="12dp"
                            android:src="@drawable/ic_check"
                            app:tint="@color/blue_primary" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="优先客服支持和技术咨询"
                            android:textColor="@color/text_color"
                            android:textSize="14sp" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 开通按钮 -->
            <Button
                android:id="@+id/btn_activate_vip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:background="@drawable/bg_button_gradient"
                android:padding="12dp"
                android:text="立即开通会员"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- 服务条款 -->
            <TextView
                android:id="@+id/terms_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="8dp"
                android:text="开通即表示同意《服务协议》和《隐私政策》"
                android:textColor="@color/text_secondary"
                android:textSize="12sp" />
        </LinearLayout>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout> 