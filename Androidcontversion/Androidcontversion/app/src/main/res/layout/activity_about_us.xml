<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="@color/background_color"
    tools:context=".AboutUsActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Header -->
        <LinearLayout
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/primary_color"
            android:elevation="2dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="15dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/btn_back"
                android:layout_width="30dp"
                android:layout_height="30dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/back"
            android:src="@drawable/ic_back"
                app:tint="@color/white" />

        <TextView
                android:layout_width="0dp"
            android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
            android:text="@string/about_us"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <View
                android:layout_width="30dp"
                android:layout_height="30dp" />

        </LinearLayout>

        <!-- Divider -->
        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:background="@color/background_color"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/header" />

        <!-- Company Logo -->
        <androidx.cardview.widget.CardView
            android:id="@+id/company_logo"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="50dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider">

            <ImageView
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="center"
                android:contentDescription="@string/company_logo"
                android:src="@drawable/ic_mobile"
                app:tint="#1EC0FF" />
        </androidx.cardview.widget.CardView>

        <!-- Main content -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/company_logo">

            <!-- Company Information Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="15dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingBottom="10dp"
                        android:text="@string/company_info"
                        android:textColor="#333"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginBottom="15dp"
                        android:background="#f0f0f0" />

                    <!-- Company Name -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingVertical="12dp">

                        <TextView
                            android:layout_width="100dp"
                            android:layout_height="wrap_content"
                            android:text="@string/company_name"
                            android:textColor="#888"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/company_name_value"
                            android:textColor="#333"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#f9f9f9" />

                    <!-- Established Year -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingVertical="12dp">

                        <TextView
                            android:layout_width="100dp"
                            android:layout_height="wrap_content"
                            android:text="@string/established_year"
                            android:textColor="#888"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/established_year_value"
                            android:textColor="#333"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#f9f9f9" />

                    <!-- Product Name -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingVertical="12dp">

                        <TextView
                            android:layout_width="100dp"
                            android:layout_height="wrap_content"
                            android:text="@string/product_name"
                            android:textColor="#888"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/product_name_value"
                            android:textColor="#333"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#f9f9f9" />

                    <!-- Current Version -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingVertical="12dp">

                        <TextView
                            android:layout_width="100dp"
                            android:layout_height="wrap_content"
                            android:text="@string/current_version"
                            android:textColor="#888"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/current_version_value"
                            android:textColor="#333"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Company Introduction Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="15dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingBottom="10dp"
                        android:text="@string/company_intro"
                        android:textColor="#333"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginBottom="15dp"
                        android:background="#f0f0f0" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="15dp"
                        android:lineSpacingExtra="4dp"
                        android:text="@string/company_desc_1"
                        android:textColor="#666"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:lineSpacingExtra="4dp"
                        android:text="@string/company_desc_2"
                        android:textColor="#666"
                        android:textSize="14sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Product Features Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="15dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingBottom="10dp"
                        android:text="@string/product_features"
                        android:textColor="#333"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginBottom="15dp"
                        android:background="#f0f0f0" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:drawableStart="@drawable/ic_bullet"
                            android:drawablePadding="8dp"
                            android:text="@string/feature_1"
                            android:textColor="#666"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:drawableStart="@drawable/ic_bullet"
                            android:drawablePadding="8dp"
                            android:text="@string/feature_2"
                            android:textColor="#666"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:drawableStart="@drawable/ic_bullet"
                            android:drawablePadding="8dp"
                            android:text="@string/feature_3"
                            android:textColor="#666"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:drawableStart="@drawable/ic_bullet"
                            android:drawablePadding="8dp"
                            android:text="@string/feature_4"
                            android:textColor="#666"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:drawableStart="@drawable/ic_bullet"
                            android:drawablePadding="8dp"
                            android:text="@string/feature_5"
                            android:textColor="#666"
                            android:textSize="14sp" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Contact Us Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="15dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingBottom="10dp"
                        android:text="@string/contact_us"
                        android:textColor="#333"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginBottom="15dp"
                        android:background="#f0f0f0" />

                    <!-- Phone -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <androidx.cardview.widget.CardView
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:layout_marginEnd="15dp"
                            app:cardBackgroundColor="#f0f8ff"
                            app:cardCornerRadius="15dp"
                            app:cardElevation="0dp">

                            <ImageView
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:layout_gravity="center"
                                android:contentDescription="@string/phone"
                                android:src="@drawable/ic_phone"
                                app:tint="#1EC0FF" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/customer_service_phone"
                            android:textColor="#666"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <!-- QQ -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <androidx.cardview.widget.CardView
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:layout_marginEnd="15dp"
                            app:cardBackgroundColor="#f0f8ff"
                            app:cardCornerRadius="15dp"
                            app:cardElevation="0dp">

                            <ImageView
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:layout_gravity="center"
                                android:contentDescription="@string/qq_item"
                                android:src="@drawable/ic_qq"
                                app:tint="#1EC0FF" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/customer_service_qq"
                            android:textColor="#666"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <!-- Email -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <androidx.cardview.widget.CardView
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:layout_marginEnd="15dp"
                            app:cardBackgroundColor="#f0f8ff"
                            app:cardCornerRadius="15dp"
                            app:cardElevation="0dp">

                            <ImageView
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:layout_gravity="center"
                                android:contentDescription="@string/email"
                                android:src="@drawable/ic_email"
                                app:tint="#1EC0FF" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/customer_service_email"
                            android:textColor="#666"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <!-- Address -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <androidx.cardview.widget.CardView
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:layout_marginEnd="15dp"
                            app:cardBackgroundColor="#f0f8ff"
                            app:cardCornerRadius="15dp"
                            app:cardElevation="0dp">

                            <ImageView
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:layout_gravity="center"
                                android:contentDescription="@string/address"
                                android:src="@drawable/ic_location"
                                app:tint="#1EC0FF" />
                        </androidx.cardview.widget.CardView>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/company_address"
                            android:textColor="#666"
                            android:textSize="14sp" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Bottom Area with Social Links -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="20dp">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="15dp"
                    android:orientation="horizontal">

                    <androidx.cardview.widget.CardView
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:layout_marginHorizontal="10dp"
                        app:cardBackgroundColor="#f5f5f5"
                        app:cardCornerRadius="18dp"
                        app:cardElevation="0dp">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            android:contentDescription="@string/wechat_item"
                            android:src="@drawable/ic_wechat"
                            app:tint="#666" />
                    </androidx.cardview.widget.CardView>

                    <androidx.cardview.widget.CardView
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:layout_marginHorizontal="10dp"
                        app:cardBackgroundColor="#f5f5f5"
                        app:cardCornerRadius="18dp"
                        app:cardElevation="0dp">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            android:contentDescription="@string/weibo"
                            android:src="@drawable/ic_weibo"
                            app:tint="#666" />
                    </androidx.cardview.widget.CardView>

                    <androidx.cardview.widget.CardView
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:layout_marginHorizontal="10dp"
                        app:cardBackgroundColor="#f5f5f5"
                        app:cardCornerRadius="18dp"
                        app:cardElevation="0dp">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            android:contentDescription="@string/website"
                            android:src="@drawable/ic_globe"
                            app:tint="#666" />
                    </androidx.cardview.widget.CardView>
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/copyright"
                    android:textColor="#999"
                    android:textSize="12sp" />

                <Button
                    android:id="@+id/btn_back_to_profile"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:background="@drawable/bg_button_gradient"
                    android:paddingVertical="12dp"
                    android:text="@string/back_to_profile"
                    android:textColor="@android:color/white"
                    android:textSize="14sp"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView> 