<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 订单标题和状态 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/order_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="手机数据恢复服务"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/order_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/order_status_processing"
                android:textColor="@color/primary_color"
                android:textSize="14sp" />
        </LinearLayout>

        <!-- 订单信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="12dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/order_number"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/order_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="SJ20230615001"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/order_time"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/order_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2023-06-15 14:30"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/order_amount"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/order_amount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="¥159.00"
                    android:textColor="@color/warning_color"
                    android:textSize="14sp"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>

        <!-- 订单操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="16dp"
            android:gravity="end">

            <Button
                android:id="@+id/btn_cancel_order"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="@string/cancel_order"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:background="@drawable/bg_button_outline"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/btn_order_detail"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="@string/view_order_detail"
                android:textSize="12sp"
                android:textColor="@color/white"
                android:background="@drawable/bg_button_primary" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView> 