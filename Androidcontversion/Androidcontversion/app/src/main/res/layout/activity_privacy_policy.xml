<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".PrivacyPolicyActivity">

    <!-- 顶部导航栏 -->
    <RelativeLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:elevation="2dp"
        android:padding="15dp">

        <ImageButton
            android:id="@+id/backBtn"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/back"
            android:padding="8dp"
            android:src="@drawable/ic_arrow_back"
            app:tint="@color/text_primary" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/privacy_policy"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold" />
    </RelativeLayout>

    <!-- 分隔线 -->
    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@id/header"
        android:background="@color/divider" />

    <!-- 内容区域 -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/divider"
        android:fillViewport="true">

        <TextView
            android:id="@+id/policyContent"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:lineSpacingExtra="4dp"
            android:padding="20dp"
            android:textColor="@color/text_primary"
            android:textSize="16sp" />
    </androidx.core.widget.NestedScrollView>

</RelativeLayout> 