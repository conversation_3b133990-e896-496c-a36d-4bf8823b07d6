<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#5367e4"
    tools:context=".PaymentActivity">

    <!-- 头部 - 状态栏已删除 -->
    <LinearLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#5367e4"
        android:padding="15dp"
        android:layout_marginTop="10dp"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <ImageButton
            android:id="@+id/back_btn"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_back"
            android:contentDescription="返回"
            app:tint="@color/white" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="服务详情"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="normal" />

        <View
            android:layout_width="24dp"
            android:layout_height="24dp" />
    </LinearLayout>

    <!-- 最近购买通知替换为滚动资讯 -->
    <include
        android:id="@+id/payment_news_ticker"
        layout="@layout/news_ticker_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="60dp"
        android:layout_gravity="top"
        android:elevation="1dp" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="100dp"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 套餐选择 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical"
                android:padding="20dp"
                android:layout_marginBottom="1dp"
                android:elevation="1dp">

                <!-- 标题 -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="15dp">

                    <View
                        android:layout_width="4dp"
                        android:layout_height="16dp"
                        android:background="#5367e4"
                        android:layout_gravity="center_vertical" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:text="选择开通服务"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <!-- 高级图片清除套餐 -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/permanent_package"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="15dp"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="2dp">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="15dp">

                        <!-- 超值爆款标签已删除 -->

                        <TextView
                            android:id="@+id/title_permanent"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="图片清除(高级版)"
                            android:textColor="#3a4db1"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:layout_marginTop="5dp" />

                        <TextView
                            android:id="@+id/desc_permanent"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="该服务可长期无上限清除相册、微信、QQ图片清除"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp"
                            android:layout_below="@id/title_permanent"
                            android:layout_marginEnd="80dp" />

                        <LinearLayout
                            android:id="@+id/validity_badge_permanent"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_below="@id/desc_permanent"
                            android:layout_marginTop="10dp"
                            android:gravity="center_vertical">

                            <ImageView
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:src="@drawable/ic_clock"
                                app:tint="#ff3b30" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="优质服务"
                                android:textColor="#ff3b30"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:layout_marginStart="5dp" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/price_permanent"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="¥158"
                            android:textColor="#5367e4"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:layout_alignParentEnd="true"
                            android:layout_alignParentTop="true" />

                        <TextView
                            android:id="@+id/original_price_permanent"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="¥198"
                            android:textColor="@color/text_tertiary"
                            android:textSize="14sp"
                            android:textStyle="normal"
                            android:layout_below="@id/price_permanent"
                            android:layout_alignParentEnd="true" />

                        <ImageView
                            android:id="@+id/check_permanent"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_check_circle"
                            android:layout_alignParentEnd="true"
                            android:layout_alignParentBottom="true"
                            app:tint="#5367e4" />
                    </RelativeLayout>
                </androidx.cardview.widget.CardView>

                <!-- 一年图片清除套餐 -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/annual_package"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="2dp">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="15dp">

                        <TextView
                            android:id="@+id/title_annual"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="图片清除(一年)"
                            android:textColor="#3a4db1"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/desc_annual"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="该服务在一年内可以无上限清除相册、微信、QQ图片清除"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp"
                            android:layout_below="@id/title_annual"
                            android:layout_marginEnd="80dp" />

                        <LinearLayout
                            android:id="@+id/validity_badge_annual"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_below="@id/desc_annual"
                            android:layout_marginTop="10dp"
                            android:gravity="center_vertical">

                            <ImageView
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:src="@drawable/ic_clock"
                                app:tint="#ff3b30" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="1年有效"
                                android:textColor="#ff3b30"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:layout_marginStart="5dp" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/price_annual"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="¥78"
                            android:textColor="#5367e4"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:layout_alignParentEnd="true"
                            android:layout_alignParentTop="true" />

                        <TextView
                            android:id="@+id/original_price_annual"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="¥98"
                            android:textColor="@color/text_tertiary"
                            android:textSize="14sp"
                            android:textStyle="normal"
                            android:layout_below="@id/price_annual"
                            android:layout_alignParentEnd="true" />

                        <ImageView
                            android:id="@+id/check_annual"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_check_circle"
                            android:layout_alignParentEnd="true"
                            android:layout_alignParentBottom="true"
                            app:tint="#5367e4"
                            android:visibility="gone" />
                    </RelativeLayout>
                </androidx.cardview.widget.CardView>
            </LinearLayout>

            <!-- 使用场景 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical"
                android:padding="20dp"
                android:layout_marginTop="8dp"
                android:elevation="1dp">

                <!-- 标题 -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="15dp">

                    <View
                        android:layout_width="4dp"
                        android:layout_height="16dp"
                        android:background="#5367e4"
                        android:layout_gravity="center_vertical" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:text="使用场景"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <!-- 场景标签 -->
                <com.google.android.material.chip.ChipGroup
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:chipSpacing="10dp">

                    <com.google.android.material.chip.Chip
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="手机图片彻底删除"
                        app:chipBackgroundColor="#eef1f6"
                        android:textColor="@color/text_secondary"
                        app:chipStrokeWidth="0dp" />

                    <com.google.android.material.chip.Chip
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="选择清除手机图片"
                        app:chipBackgroundColor="#eef1f6"
                        android:textColor="@color/text_secondary"
                        app:chipStrokeWidth="0dp" />

                    <com.google.android.material.chip.Chip
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="扫描手机图片"
                        app:chipBackgroundColor="#eef1f6"
                        android:textColor="@color/text_secondary"
                        app:chipStrokeWidth="0dp" />
                </com.google.android.material.chip.ChipGroup>
            </LinearLayout>

            <!-- 用户评价 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical"
                android:padding="20dp"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="80dp"
                android:elevation="1dp">

                <!-- 标题 -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="15dp">

                    <View
                        android:layout_width="4dp"
                        android:layout_height="16dp"
                        android:background="#5367e4"
                        android:layout_gravity="center_vertical" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:text="优质用户评价"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <!-- 评价列表 -->
                <!-- 第一个评价 -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="15dp"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="15dp">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="10dp">

                            <TextView
                                android:id="@+id/review_avatar_1"
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:background="@drawable/circle_avatar_bg"
                                android:gravity="center"
                                android:text="冬"
                                android:textColor="@color/white"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="冬季温暖优雅"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:layout_toEndOf="@id/review_avatar_1"
                                android:layout_centerVertical="true"
                                android:layout_marginStart="10dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="⭐ 5分"
                                android:textColor="#ffd700"
                                android:textSize="12sp"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true" />
                        </RelativeLayout>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="我非常满意数据修复服务的效果。他们专业的团队帮助我成功修复了丢失的文件，让我感到非常安心和放心。他们高效的工作速度和专业的技术水平让我对他们的服务印象深刻。强烈推荐！"
                            android:textColor="@color/text_secondary"
                            android:textSize="13sp"
                            android:lineSpacingExtra="3dp" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <!-- 第二个评价 -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="15dp"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="15dp">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="10dp">

                            <TextView
                                android:id="@+id/review_avatar_2"
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:background="@drawable/circle_avatar_bg"
                                android:gravity="center"
                                android:text="S"
                                android:textColor="@color/white"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Starlight99"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:layout_toEndOf="@id/review_avatar_2"
                                android:layout_centerVertical="true"
                                android:layout_marginStart="10dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="⭐ 5分"
                                android:textColor="#ffd700"
                                android:textSize="12sp"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true" />
                        </RelativeLayout>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="我遇到了数据丢失的问题，但幸运的是找到了文件数据修复大师。他们的技术团队非常专业，通过他们的努力，我成功地修复了关键文件。他们提供的服务超出了我的期望，我对他们的工作非常满意。如果您需要数据修复，我强烈推荐选择他们！"
                            android:textColor="@color/text_secondary"
                            android:textSize="13sp"
                            android:lineSpacingExtra="3dp" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <!-- 第三个评价 -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="15dp"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="15dp">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="10dp">

                            <TextView
                                android:id="@+id/review_avatar_3"
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:background="@drawable/circle_avatar_bg"
                                android:gravity="center"
                                android:text="F"
                                android:textColor="@color/white"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="FreeSpirit777"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:layout_toEndOf="@id/review_avatar_3"
                                android:layout_centerVertical="true"
                                android:layout_marginStart="10dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="⭐ 5分"
                                android:textColor="#ffd700"
                                android:textSize="12sp"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true" />
                        </RelativeLayout>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="文件数据修复大师为我提供了出色的帮助。他们的团队非常耐心地解释了整个修复过程，并及时与我沟通进展。最重要的是，他们成功地找回了我认为已经永远丢失的数据。感谢他们的专业和高效服务！"
                            android:textColor="@color/text_secondary"
                            android:textSize="13sp"
                            android:lineSpacingExtra="3dp" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="随机挑选3条优质评论"
                    android:textColor="@color/text_tertiary"
                    android:textSize="12sp"
                    android:gravity="center"
                    android:layout_marginTop="15dp" />
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <!-- 底部操作栏 -->
    <LinearLayout
        android:id="@+id/bottom_action"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/white"
        android:padding="15dp"
        android:elevation="8dp"
        android:layout_gravity="bottom">

        <Button
            android:id="@+id/pay_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/orange_gradient_button_bg"
            android:text="立即购买"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            android:padding="15dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginTop="10dp">
            
            <TextView
                android:id="@+id/terms_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="支付即代表您已阅读并同意"
                android:textColor="@color/text_secondary"
                android:textSize="12sp" />
                
            <TextView
                android:id="@+id/service_terms_link"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="《服务说明》"
                android:textColor="#5367e4"
                android:textSize="12sp"
                android:textStyle="bold" />
        </LinearLayout>
    </LinearLayout>

    <!-- 支付处理中布局（默认隐藏） -->
    <LinearLayout
        android:id="@+id/processing_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:background="#80000000"
        android:visibility="gone">

        <androidx.cardview.widget.CardView
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            app:cardCornerRadius="15dp"
            app:cardElevation="5dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/payment_processing"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="20dp" />

                <ProgressBar
                    android:id="@+id/progress_bar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="20dp"
                    android:indeterminateTint="#5367e4" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/wait_for_payment"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout> 