<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".FileSendActivity">

    <!-- 顶部导航栏 -->
    <RelativeLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:elevation="2dp"
        android:padding="15dp">

        <ImageButton
            android:id="@+id/back_btn"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/back"
            android:padding="8dp"
            android:src="@drawable/ic_arrow_back"
            app:tint="@color/text_primary" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/file_send"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold" />
    </RelativeLayout>

    <!-- 添加文件按钮 -->
    <Button
        android:id="@+id/add_file_btn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/header"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/bg_button_outline_primary"
        android:drawableStart="@drawable/ic_add"
        android:drawablePadding="8dp"
        android:paddingHorizontal="16dp"
        android:paddingVertical="12dp"
        android:text="@string/add_file"
        android:textColor="@color/blue_primary"
        android:textSize="16sp" />

    <!-- 空状态提示 -->
    <TextView
        android:id="@+id/empty_state_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:text="@string/please_select_files"
        android:textColor="@color/text_secondary"
        android:textSize="16sp" />

    <!-- 文件列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/file_list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/send_file_btn"
        android:layout_below="@id/add_file_btn"
        android:layout_marginTop="16dp"
        android:clipToPadding="false"
        android:padding="16dp"
        android:visibility="gone" />

    <!-- 发送中布局 -->
    <LinearLayout
        android:id="@+id/sending_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/add_file_btn"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="16dp"
        android:orientation="vertical"
        android:visibility="gone">

        <ProgressBar
            android:id="@+id/send_progress"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="4dp"
            android:layout_marginBottom="8dp"
            android:progressDrawable="@drawable/progress_bar_gradient" />

        <TextView
            android:id="@+id/sending_progress_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:text="@string/sending_progress"
            android:textColor="@color/text_secondary"
            android:textSize="12sp" />
    </LinearLayout>

    <!-- 发送按钮 -->
    <Button
        android:id="@+id/send_file_btn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_margin="16dp"
        android:background="@drawable/bg_button_gradient"
        android:drawableStart="@drawable/ic_send"
        android:drawablePadding="8dp"
        android:paddingStart="20dp"
        android:paddingEnd="30dp"
        android:text="@string/send_file"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold" />

</RelativeLayout> 