<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f5f5f5"
    tools:context=".SupervisionComplaintActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 头部导航栏 -->
        <RelativeLayout
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:padding="15dp">

            <ImageButton
                android:id="@+id/backBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:background="@null"
                android:contentDescription="@string/back"
                android:text="&#10094;"
                android:textColor="@color/text_primary"
                android:textSize="24sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/supervision_complaint"
                android:textColor="@color/text_primary"
                android:textSize="18sp"
                android:textStyle="bold" />
        </RelativeLayout>

        <!-- 灰色分隔线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:background="#f5f5f5"
            android:layerType="software" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <!-- 投诉类型 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="30dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="15px"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="*"
                            android:textColor="#e53935"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="5dp"
                            android:text="@string/complaint_type"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp" />
                    </LinearLayout>

                    <Spinner
                        android:id="@+id/complaintTypeSpinner"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_edit_text"
                        android:padding="15dp" />
                </LinearLayout>

                <!-- 联系方式 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="30dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="15px"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="*"
                            android:textColor="#e53935"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="5dp"
                            android:text="@string/contact_method"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp" />
                    </LinearLayout>

                    <EditText
                        android:id="@+id/contactInput"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_edit_text"
                        android:hint="@string/enter_phone_number"
                        android:inputType="phone"
                        android:padding="15dp"
                        android:textColor="@color/text_primary"
                        android:textColorHint="@color/text_tertiary"
                        android:textSize="16sp" />
                </LinearLayout>

                <!-- 订单号 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="30dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="15px"
                        android:text="@string/complaint_order_number"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp" />

                    <EditText
                        android:id="@+id/orderNumberInput"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_edit_text"
                        android:hint="@string/enter_order_number"
                        android:inputType="text"
                        android:padding="15dp"
                        android:textColor="@color/text_primary"
                        android:textColorHint="@color/text_tertiary"
                        android:textSize="16sp" />
                </LinearLayout>

                <!-- 投诉内容 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="30dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="15px"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="*"
                            android:textColor="#e53935"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="5dp"
                            android:text="@string/complaint_content"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp" />
                    </LinearLayout>

                    <EditText
                        android:id="@+id/complaintContentInput"
                        android:layout_width="match_parent"
                        android:layout_height="150dp"
                        android:background="@drawable/bg_edit_text"
                        android:gravity="top"
                        android:hint="@string/describe_issue_detail"
                        android:inputType="textMultiLine"
                        android:padding="15dp"
                        android:textColor="@color/text_primary"
                        android:textColorHint="@color/text_tertiary"
                        android:textSize="16sp" />
                </LinearLayout>

                <!-- 隐私协议 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="20dp"
                    android:orientation="horizontal"
                    android:gravity="start">

                    <CheckBox
                        android:id="@+id/privacyCheckbox"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="10dp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/privacy_agreement"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />
                </LinearLayout>

                <!-- 说明文字 -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/processing_notice"
                    android:textColor="@color/text_tertiary"
                    android:textSize="14sp" />
            </LinearLayout>
        </ScrollView>

        <!-- 底部提交按钮 -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="20dp">

            <Button
                android:id="@+id/submitBtn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_button_blue_gradient"
                android:padding="15dp"
                android:text="@string/submit_complaint"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />
        </RelativeLayout>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 