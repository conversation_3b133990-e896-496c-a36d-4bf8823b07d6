<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    tools:context=".ScanActivity">

    <!-- Header -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
        android:background="@color/background_color"
        android:elevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/back"
                android:src="@drawable/ic_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/scanning"
                android:textColor="@color/text_color"
                android:textSize="18sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.Toolbar>

    <!-- 扫描屏幕 (始终可见) -->
    <FrameLayout
        android:id="@+id/scanning_screen"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/background_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
                android:orientation="vertical">

            <FrameLayout
                android:layout_width="140dp"
                android:layout_height="140dp"
                android:layout_marginBottom="40dp">

                <View
                    android:id="@+id/scan_circle_outer"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/bg_scan_circle_outer" />

                <View
                    android:id="@+id/scan_circle_middle"
                        android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="15dp"
                    android:background="@drawable/bg_scan_circle_middle" />

                <View
                    android:id="@+id/scan_circle_inner"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                    android:layout_margin="30dp"
                    android:background="@drawable/bg_scan_circle_inner" />

                            <TextView
                    android:id="@+id/scan_icon"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                    android:text="📄"
                    android:textColor="@color/primary_color"
                    android:textSize="40sp" />
                        </FrameLayout>

                        <TextView
                android:id="@+id/scanning_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="15dp"
                android:text="@string/scanning_files"
                android:textColor="@color/text_color"
                android:textSize="22sp"
                            android:textStyle="bold" />

                        <TextView
                android:id="@+id/scanning_desc"
                android:layout_width="280dp"
                            android:layout_height="wrap_content"
                android:layout_marginBottom="30dp"
                                    android:gravity="center"
                android:lineSpacingExtra="3dp"
                android:text="@string/scanning_files_desc"
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />

            <ProgressBar
                android:id="@+id/scan_progress_bar"
                style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                android:layout_width="300dp"
                android:layout_height="8dp"
                            android:layout_marginBottom="10dp"
                android:max="100"
                android:progress="0"
                android:progressDrawable="@drawable/progress_bar_gradient" />

                        <TextView
                android:id="@+id/tv_scan_percentage"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                android:layout_marginBottom="30dp"
                android:text="0%"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_scan_files"
                            android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:alpha="0"
                android:text="@string/found_files"
                android:textColor="@color/text_muted"
                    android:textSize="14sp" />
            </LinearLayout>
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout> 