<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".ContactServiceActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header -->
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/primary_color"
            android:elevation="4dp"
            app:contentInsetStart="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <ImageButton
                    android:id="@+id/btn_back"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="@string/back"
                    android:padding="12dp"
                    android:src="@drawable/ic_back"
                    app:tint="@android:color/white" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/contact_us"
                    android:textColor="@android:color/white"
                    android:textSize="20sp"
                    android:textStyle="normal" />

                <View
                    android:layout_width="48dp"
                    android:layout_height="48dp" />
            </LinearLayout>
        </androidx.appcompat.widget.Toolbar>

        <!-- Service Hours Banner -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:background="@drawable/bg_service_hours"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="15dp">

            <ImageView
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_marginEnd="10dp"
                android:contentDescription="@string/service_hours"
                android:src="@drawable/ic_clock"
                app:tint="@color/service_hours_text" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/service_hours"
                android:textColor="@color/service_hours_text"
                android:textSize="13sp" />
        </LinearLayout>

        <!-- Contact Methods Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="15dp"
                android:drawableStart="@drawable/bg_section_indicator"
                android:drawablePadding="8dp"
                android:text="@string/contact_methods"
                android:textColor="@color/text_color"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- Online Support Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="15dp"
                app:cardBackgroundColor="@android:color/white"
                app:cardCornerRadius="10dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="15dp">

                    <FrameLayout
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/bg_contact_icon"
                        android:layout_marginEnd="15dp">

                        <ImageView
                            android:layout_width="18dp"
                            android:layout_height="18dp"
                            android:layout_gravity="center"
                            android:contentDescription="@string/online_support"
                            android:src="@drawable/ic_headset"
                            app:tint="@color/contact_primary" />
                    </FrameLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/online_support"
                            android:textColor="@color/text_color"
                            android:textSize="15sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/online_support_desc"
                            android:textColor="@color/text_secondary"
                            android:textSize="13sp" />
                    </LinearLayout>

                    <Button
                        android:id="@+id/btn_consult"
                        android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:background="@drawable/bg_contact_action"
                        android:text="@string/consult"
                        android:textColor="@color/contact_primary"
                        android:textSize="13sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Hotline Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="15dp"
                app:cardBackgroundColor="@android:color/white"
                app:cardCornerRadius="10dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="15dp">

                    <FrameLayout
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/bg_contact_icon"
                        android:layout_marginEnd="15dp">

                        <ImageView
                            android:layout_width="18dp"
                            android:layout_height="18dp"
                            android:layout_gravity="center"
                            android:contentDescription="@string/hotline"
                            android:src="@drawable/ic_phone"
                            app:tint="@color/contact_primary" />
                    </FrameLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/hotline"
                            android:textColor="@color/text_color"
                            android:textSize="15sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/hotline_number"
                            android:textColor="@color/text_secondary"
                            android:textSize="13sp" />
                    </LinearLayout>

                    <Button
                        android:id="@+id/btn_call"
                        android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:background="@drawable/bg_contact_action"
                        android:text="@string/call"
                        android:textColor="@color/contact_primary"
                        android:textSize="13sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Email Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="15dp"
                app:cardBackgroundColor="@android:color/white"
                app:cardCornerRadius="10dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="15dp">

                    <FrameLayout
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/bg_contact_icon"
                        android:layout_marginEnd="15dp">

                        <ImageView
                            android:layout_width="18dp"
                            android:layout_height="18dp"
                            android:layout_gravity="center"
                            android:contentDescription="@string/email"
                            android:src="@drawable/ic_email"
                            app:tint="@color/contact_primary" />
                    </FrameLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/email"
                            android:textColor="@color/text_color"
                            android:textSize="15sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/support_email"
                            android:textColor="@color/text_secondary"
                            android:textSize="13sp" />
                    </LinearLayout>

                    <Button
                        android:id="@+id/btn_send_email"
                        android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:background="@drawable/bg_contact_action"
                        android:text="@string/send"
                        android:textColor="@color/contact_primary"
                        android:textSize="13sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:background="@color/background_light" />

        <!-- Question Form Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="15dp"
                android:drawableStart="@drawable/bg_section_indicator"
                android:drawablePadding="8dp"
                android:text="@string/submit_question"
                android:textColor="@color/text_color"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="@string/question_type"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:textStyle="bold" />

            <Spinner
                android:id="@+id/spinner_question_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="15dp"
                android:background="@drawable/bg_edit_text"
                android:minHeight="48dp"
                android:padding="12dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="@string/contact_info"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:textStyle="bold" />

            <EditText
                android:id="@+id/et_contact_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="15dp"
                android:background="@drawable/bg_edit_text"
                android:hint="@string/contact_info_hint"
                android:inputType="phone"
                android:minHeight="48dp"
                android:padding="12dp"
                android:textSize="14sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="@string/question_description"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:textStyle="bold" />

            <EditText
                android:id="@+id/et_question_description"
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:layout_marginBottom="20dp"
                android:background="@drawable/bg_edit_text"
                android:gravity="top|start"
                android:hint="@string/question_description_hint"
                android:inputType="textMultiLine"
                android:padding="12dp"
                android:textSize="14sp" />

            <Button
                android:id="@+id/btn_submit_question"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_gradient_button"
                android:text="@string/submit_question"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:background="@color/background_light" />

        <!-- FAQ Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="15dp"
                android:drawableStart="@drawable/bg_section_indicator"
                android:drawablePadding="8dp"
                android:text="@string/faq"
                android:textColor="@color/text_color"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- FAQ Item 1 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                app:cardBackgroundColor="@android:color/white"
                app:cardCornerRadius="10dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="15dp">

                    <LinearLayout
                        android:id="@+id/faq_header_1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/faq_recovery_time_question"
                            android:textColor="@color/text_color"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/faq_toggle_1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="+"
                            android:textColor="@color/contact_primary"
                            android:textSize="18sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/faq_answer_1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:lineSpacingExtra="2dp"
                        android:paddingTop="10dp"
                        android:text="@string/faq_recovery_time_answer"
                        android:textColor="@color/text_secondary"
                        android:textSize="13sp"
                        android:visibility="gone" />

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- FAQ Item 2 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                app:cardBackgroundColor="@android:color/white"
                app:cardCornerRadius="10dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="15dp">

                    <LinearLayout
                        android:id="@+id/faq_header_2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/faq_success_rate_question"
                            android:textColor="@color/text_color"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/faq_toggle_2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="+"
                            android:textColor="@color/contact_primary"
                            android:textSize="18sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/faq_answer_2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:lineSpacingExtra="2dp"
                        android:paddingTop="10dp"
                        android:text="@string/faq_success_rate_answer"
                        android:textColor="@color/text_secondary"
                        android:textSize="13sp"
                        android:visibility="gone" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- FAQ Item 3 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                app:cardBackgroundColor="@android:color/white"
                app:cardCornerRadius="10dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="15dp">

                    <LinearLayout
                        android:id="@+id/faq_header_3"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/faq_payment_question"
                            android:textColor="@color/text_color"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/faq_toggle_3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="+"
                            android:textColor="@color/contact_primary"
                            android:textSize="18sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/faq_answer_3"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:lineSpacingExtra="2dp"
                        android:paddingTop="10dp"
                        android:text="@string/faq_payment_answer"
                        android:textColor="@color/text_secondary"
                        android:textSize="13sp"
                        android:visibility="gone" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- FAQ Item 4 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@android:color/white"
                app:cardCornerRadius="10dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="15dp">

                    <LinearLayout
                        android:id="@+id/faq_header_4"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/faq_cancel_question"
                            android:textColor="@color/text_color"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/faq_toggle_4"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="+"
                            android:textColor="@color/contact_primary"
                            android:textSize="18sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/faq_answer_4"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:lineSpacingExtra="2dp"
                        android:paddingTop="10dp"
                        android:text="@string/faq_cancel_answer"
                        android:textColor="@color/text_secondary"
                        android:textSize="13sp"
                        android:visibility="gone" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </LinearLayout>
</ScrollView> 