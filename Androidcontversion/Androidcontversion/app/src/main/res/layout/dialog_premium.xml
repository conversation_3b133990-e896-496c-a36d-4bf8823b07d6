<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="20dp"
    app:cardCornerRadius="15dp"
    app:cardElevation="6dp">

    <!-- 使用ConstraintLayout作为容器，可以更灵活地控制布局 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- 顶部渐变背景区域 -->
        <View
            android:id="@+id/header_bg"
            android:layout_width="0dp"
            android:layout_height="120dp"
            android:background="@drawable/bg_premium_header"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 特权图标 -->
        <ImageView
            android:id="@+id/crown_icon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginTop="24dp"
            android:src="@drawable/ic_shield"
            android:tint="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 关闭按钮 -->
        <ImageButton
            android:id="@+id/btn_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_margin="12dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            android:tint="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 会员解锁标题 -->
        <TextView
            android:id="@+id/premium_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/unlock_member_feature"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/crown_icon" />

        <!-- 内容区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp"
            app:layout_constraintTop_toBottomOf="@id/header_bg">

            <!-- 会员特权列表 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="20dp">
                
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="15dp"
                    android:text="开通会员即可享受："
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:textStyle="bold" />
                
                <!-- 特权项 1 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="10dp">
                    
                    <ImageView
                        android:layout_width="18dp"
                        android:layout_height="18dp"
                        android:layout_marginEnd="10dp"
                        android:src="@drawable/ic_check"
                        android:tint="@color/blue_primary"/>
                    
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="所有数据恢复功能无限使用"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp"/>
                </LinearLayout>
                
                <!-- 特权项 2 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="10dp">
                    
                    <ImageView
                        android:layout_width="18dp"
                        android:layout_height="18dp"
                        android:layout_marginEnd="10dp"
                        android:src="@drawable/ic_check"
                        android:tint="@color/blue_primary"/>
                    
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="专业技术人员一对一服务"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp"/>
                </LinearLayout>
                
                <!-- 特权项 3 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    
                    <ImageView
                        android:layout_width="18dp"
                        android:layout_height="18dp"
                        android:layout_marginEnd="10dp"
                        android:src="@drawable/ic_check"
                        android:tint="@color/blue_primary"/>
                    
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="高级恢复功能优先使用权"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp"/>
                </LinearLayout>
            </LinearLayout>

            <!-- 按钮 -->
            <Button
                android:id="@+id/btn_activate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_button_gradient"
                android:padding="14dp"
                android:text="立即开通会员"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                android:elevation="2dp" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView> 