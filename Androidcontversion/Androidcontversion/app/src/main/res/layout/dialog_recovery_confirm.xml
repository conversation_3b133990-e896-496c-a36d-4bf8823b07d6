<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/white"
    android:orientation="vertical"
    android:padding="20dp">

    <TextView
        android:id="@+id/tv_dialog_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="15dp"
        android:gravity="center"
        android:text="@string/recovery_dialog_title"
        android:textColor="@color/text_color"
        android:textSize="22sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_dialog_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="25dp"
        android:gravity="center"
        android:lineSpacingExtra="3dp"
        android:text="@string/recovery_dialog_content"
        android:textColor="@color/text_secondary"
        android:textSize="16sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:background="#f0f0f0"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:text="@string/cancel_action"
            android:textColor="@color/text_secondary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btn_confirm"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:background="@drawable/bg_button_primary"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:text="@string/recover"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:textStyle="bold" />
    </LinearLayout>
</LinearLayout> 