<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_dark"
    tools:context=".ImageScaleActivity">

    <!-- 顶部导航栏 -->
    <RelativeLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background_dark"
        android:elevation="2dp"
        android:padding="15dp"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/back_btn"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/back"
            android:padding="8dp"
            android:src="@drawable/ic_arrow_back"
            app:tint="@color/white" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/image_scale"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />
    </RelativeLayout>

    <!-- 图片预览区域 -->
    <FrameLayout
        android:id="@+id/imageContainer"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="16dp"
        android:background="@color/background_dark"
        app:layout_constraintBottom_toTopOf="@id/controlsContainer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header">

        <ImageView
            android:id="@+id/image_preview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@string/image_preview"
            android:scaleType="centerInside"
            tools:src="@drawable/sample_image" />

        <TextView
            android:id="@+id/scale_percent_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|end"
            android:layout_margin="16dp"
            android:background="#80000000"
            android:paddingHorizontal="8dp"
            android:paddingVertical="4dp"
            android:text="@string/scale_percent"
            android:textColor="@color/white"
            android:textSize="14sp" />
    </FrameLayout>

    <!-- 进度条 -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 控制区域 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/controlsContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginVertical="8dp"
        app:cardBackgroundColor="@color/background_dark_lighter"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:layout_constraintBottom_toTopOf="@id/buttonContainer">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 缩放控制 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:contentDescription="@string/scale"
                    android:src="@drawable/ic_scale"
                    app:tint="@color/white" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="@string/scale"
                    android:textColor="@color/white"
                    android:textSize="16sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="20%"
                    android:textColor="@color/white_50"
                    android:textSize="12sp" />

                <SeekBar
                    android:id="@+id/scale_seekbar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="8dp"
                    android:layout_weight="1"
                    android:max="200"
                    android:progress="100"
                    android:progressTint="@color/blue_primary"
                    android:thumbTint="@color/blue_primary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="200%"
                    android:textColor="@color/white_50"
                    android:textSize="12sp" />
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- 底部按钮 -->
    <LinearLayout
        android:id="@+id/buttonContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent">
        
        <Button
            android:id="@+id/select_image_btn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:background="@drawable/bg_button_primary"
            android:drawableStart="@drawable/ic_image"
            android:drawablePadding="8dp"
            android:gravity="center"
            android:padding="12dp"
            android:text="@string/select_image"
            android:textColor="@color/white" />

        <Button
            android:id="@+id/apply_btn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:background="@drawable/bg_button_outline"
            android:padding="12dp"
            android:text="@string/apply"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <Button
            android:id="@+id/save_btn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/bg_button_gradient"
            android:padding="12dp"
            android:text="@string/save"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 