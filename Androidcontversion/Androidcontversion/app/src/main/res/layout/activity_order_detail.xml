<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".OrderDetailActivity">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary_color"
        android:orientation="horizontal"
        android:padding="16dp"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/back_btn"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/back"
            android:src="@drawable/ic_back" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/order_detail"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <Space
            android:layout_width="24dp"
            android:layout_height="24dp" />
    </LinearLayout>

    <!-- 滚动内容 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toTopOf="@id/bottom_navigation"
        app:layout_constraintTop_toBottomOf="@id/header">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 订单状态卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/order_status_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingBottom="16dp"
                        android:text="@string/order_pending_payment"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <!-- 订单信息 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <!-- 购买服务 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="12dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/service_purchased"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/service_name"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:gravity="end"
                                android:text="图片清除(高级版)"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <!-- 订单号 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="12dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/order_number"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp" />

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:gravity="end"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/order_number"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="DISPLAYED24112210"
                                    android:textColor="@color/text_primary"
                                    android:textSize="14sp" />

                                <Button
                                    android:id="@+id/copy_order_id_btn"
                                    android:layout_width="wrap_content"
                                    android:layout_height="32dp"
                                    android:layout_marginStart="8dp"
                                    android:background="@drawable/bg_button_outline"
                                    android:minWidth="0dp"
                                    android:paddingStart="8dp"
                                    android:paddingEnd="8dp"
                                    android:text="@string/copy"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />
                            </LinearLayout>
                        </LinearLayout>

                        <!-- 购买时间 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/purchase_time"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/purchase_time"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2"
                                android:gravity="end"
                                android:text="2024-11-22 18:18:52"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </LinearLayout>

                    <!-- 价格和操作按钮 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="24dp"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/price_text"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:gravity="end"
                            android:text="¥158.0"
                            android:textColor="@color/warning_color"
                            android:textSize="22sp"
                            android:textStyle="bold" />

                        <!-- 操作按钮 -->
                        <LinearLayout
                            android:id="@+id/action_buttons"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:gravity="end"
                            android:orientation="horizontal">

                            <Button
                                android:id="@+id/cancel_order_btn"
                                android:layout_width="wrap_content"
                                android:layout_height="40dp"
                                android:layout_marginEnd="16dp"
                                android:background="@drawable/bg_button_outline"
                                android:paddingStart="16dp"
                                android:paddingEnd="16dp"
                                android:text="@string/cancel_order"
                                android:textColor="@color/text_primary" />

                            <Button
                                android:id="@+id/pay_btn"
                                android:layout_width="wrap_content"
                                android:layout_height="40dp"
                                android:background="@drawable/bg_button_primary"
                                android:paddingStart="16dp"
                                android:paddingEnd="16dp"
                                android:text="@string/go_to_pay"
                                android:textColor="@color/white" />
                        </LinearLayout>

                        <!-- 查看教程按钮（初始隐藏） -->
                        <Button
                            android:id="@+id/view_tutorial_btn"
                            android:layout_width="wrap_content"
                            android:layout_height="40dp"
                            android:layout_marginTop="16dp"
                            android:layout_gravity="end"
                            android:background="@drawable/bg_button_success"
                            android:paddingStart="16dp"
                            android:paddingEnd="16dp"
                            android:text="查看教程"
                            android:textColor="@color/white"
                            android:visibility="gone" />

                        <!-- 支付成功状态（初始隐藏） -->
                        <TextView
                            android:id="@+id/payment_success_badge"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:background="@drawable/bg_success_badge"
                            android:paddingStart="24dp"
                            android:paddingTop="10dp"
                            android:paddingEnd="24dp"
                            android:paddingBottom="10dp"
                            android:text="@string/payment_success"
                            android:textColor="@color/white"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:visibility="gone" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 订单底部文本 -->
            <TextView
                android:id="@+id/order_footer_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:gravity="center"
                android:text="@string/all_orders_shown"
                android:textColor="@color/text_tertiary"
                android:textSize="14sp" />
        </LinearLayout>
    </ScrollView>

    <!-- 底部导航 -->
    <LinearLayout
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:background="@color/white"
        android:elevation="8dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- 首页 -->
        <LinearLayout
            android:id="@+id/nav_home"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/nav_home_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:contentDescription="@string/tab_home"
                android:src="@drawable/ic_home" />

            <TextView
                android:id="@+id/nav_home_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:text="@string/tab_home"
                android:textColor="@color/text_secondary"
                android:textSize="12sp" />
        </LinearLayout>

        <!-- 案例 -->
        <LinearLayout
            android:id="@+id/nav_cases"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/nav_cases_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:contentDescription="@string/cases"
                android:src="@drawable/ic_cases" />

            <TextView
                android:id="@+id/nav_cases_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:text="@string/cases"
                android:textColor="@color/text_secondary"
                android:textSize="12sp" />
        </LinearLayout>

        <!-- 订单 -->
        <LinearLayout
            android:id="@+id/nav_order"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:orientation="vertical"
            android:background="@drawable/bottom_nav_item_background">

            <ImageView
                android:id="@+id/nav_order_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:contentDescription="@string/order"
                android:src="@drawable/ic_order_selected" />

            <TextView
                android:id="@+id/nav_order_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:text="@string/order"
                android:textColor="@color/primary_color"
                android:textSize="12sp" />
        </LinearLayout>

        <!-- 个人中心 -->
        <LinearLayout
            android:id="@+id/nav_profile"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/nav_profile_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:contentDescription="@string/profile"
                android:src="@drawable/ic_profile" />

            <TextView
                android:id="@+id/nav_profile_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:text="@string/profile"
                android:textColor="@color/text_secondary"
                android:textSize="12sp" />
        </LinearLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout> 