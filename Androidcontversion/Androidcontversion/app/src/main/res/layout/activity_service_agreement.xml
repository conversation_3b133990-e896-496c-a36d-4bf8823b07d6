<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".ServiceAgreementActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 头部 -->
        <RelativeLayout
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/blue_primary"
            android:padding="15dp">

            <ImageButton
                android:id="@+id/backBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:background="@null"
                android:contentDescription="@string/back"
                android:src="@drawable/ic_arrow_back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/service_agreement_description"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold" />
        </RelativeLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <!-- 服务概述 -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:text="@string/service_overview"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:paddingBottom="5dp"
                    android:background="@drawable/bottom_border_accent" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="25dp"
                    android:lineSpacingExtra="4dp"
                    android:text="@string/service_overview_content"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp" />

                <!-- 服务内容 -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:text="@string/service_content"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:paddingBottom="5dp"
                    android:background="@drawable/bottom_border_accent" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="25dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_dot"
                        android:drawablePadding="8dp"
                        android:text="@string/wechat_chat_recovery"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_dot"
                        android:drawablePadding="8dp"
                        android:text="@string/wechat_friends_recovery"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_dot"
                        android:drawablePadding="8dp"
                        android:text="@string/photo_recovery"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_dot"
                        android:drawablePadding="8dp"
                        android:text="@string/file_transfer"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_dot"
                        android:drawablePadding="8dp"
                        android:text="@string/call_log_recovery"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_dot"
                        android:drawablePadding="8dp"
                        android:text="@string/sms_recovery"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_dot"
                        android:drawablePadding="8dp"
                        android:text="@string/image_resize_crop"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />
                </LinearLayout>

                <!-- 服务条款 -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:text="@string/service_terms"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:paddingBottom="5dp"
                    android:background="@drawable/bottom_border_accent" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="25dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_dot"
                        android:drawablePadding="8dp"
                        android:text="@string/service_period_highlight"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_dot"
                        android:drawablePadding="8dp"
                        android:text="@string/usage_limit_highlight"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_dot"
                        android:drawablePadding="8dp"
                        android:text="@string/recovery_success_rate_highlight"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_dot"
                        android:drawablePadding="8dp"
                        android:text="@string/refund_policy_highlight"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />
                </LinearLayout>

                <!-- 隐私保护 -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:text="@string/privacy_protection"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:paddingBottom="5dp"
                    android:background="@drawable/bottom_border_accent" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:text="@string/we_value_privacy"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="10dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_dot"
                        android:drawablePadding="8dp"
                        android:text="@string/local_recovery_process"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_dot"
                        android:drawablePadding="8dp"
                        android:text="@string/no_saving_viewing_data"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_dot"
                        android:drawablePadding="8dp"
                        android:text="@string/encryption_protection"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_dot"
                        android:drawablePadding="8dp"
                        android:text="@string/no_third_party_sharing"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />
                </LinearLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="25dp"
                    android:text="@string/usage_statistics_note"
                    android:textColor="@color/red"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <!-- 使用建议 -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:text="@string/usage_suggestions"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:paddingBottom="5dp"
                    android:background="@drawable/bottom_border_accent" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="25dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_dot"
                        android:drawablePadding="8dp"
                        android:text="@string/timely_recovery_highlight"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_dot"
                        android:drawablePadding="8dp"
                        android:text="@string/stop_usage_highlight"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_dot"
                        android:drawablePadding="8dp"
                        android:text="@string/stable_network_highlight"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_dot"
                        android:drawablePadding="8dp"
                        android:text="@string/sufficient_battery_highlight"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />
                </LinearLayout>

                <!-- 联系我们 -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:text="@string/contact_us"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:paddingBottom="5dp"
                    android:background="@drawable/bottom_border_accent" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:text="@string/contact_us_intro"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="25dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_dot"
                        android:drawablePadding="8dp"
                        android:text="@string/customer_service_hotline"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_dot"
                        android:drawablePadding="8dp"
                        android:text="@string/service_support_email"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_dot"
                        android:drawablePadding="8dp"
                        android:text="@string/online_customer_service"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />
                </LinearLayout>

                <Button
                    android:id="@+id/agreeBtn"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="20dp"
                    android:background="@drawable/bg_button_primary"
                    android:text="@string/i_have_read_and_agree"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold" />
            </LinearLayout>
        </ScrollView>

        <!-- 底部版权 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/light_gray"
            android:gravity="center"
            android:padding="20dp"
            android:text="@string/copyright_text"
            android:textColor="@color/text_tertiary"
            android:textSize="12sp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 