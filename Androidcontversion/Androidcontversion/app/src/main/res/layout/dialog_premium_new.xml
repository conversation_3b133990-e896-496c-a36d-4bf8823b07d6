<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="16dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="16dp"
    app:cardElevation="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 顶部图片部分 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="160dp">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:src="@drawable/bg_gradient_blue" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="16dp">

                <ImageView
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:src="@drawable/ic_data_recovery"
                    app:tint="@color/white" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:text="需要开通会员"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <ImageButton
                android:id="@+id/btn_close"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_gravity="end|top"
                android:layout_margin="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="8dp"
                android:src="@drawable/ic_close"
                app:tint="@color/white" />
        </FrameLayout>

        <!-- 内容部分 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="此功能为VIP会员专享"
                android:textAlignment="center"
                android:textColor="@color/text_color"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="开通会员后，您将获得以下特权："
                android:textAlignment="center"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

            <!-- 特权列表 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="vertical">

                <!-- 特权1 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="12dp"
                        android:src="@drawable/ic_check"
                        app:tint="@color/blue_primary" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="使用所有高级功能"
                        android:textColor="@color/text_color"
                        android:textSize="14sp" />
                </LinearLayout>

                <!-- 特权2 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="12dp"
                        android:src="@drawable/ic_check"
                        app:tint="@color/blue_primary" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="无限次数据恢复"
                        android:textColor="@color/text_color"
                        android:textSize="14sp" />
                </LinearLayout>

                <!-- 特权3 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="12dp"
                        android:src="@drawable/ic_check"
                        app:tint="@color/blue_primary" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="优先技术支持"
                        android:textColor="@color/text_color"
                        android:textSize="14sp" />
                </LinearLayout>
            </LinearLayout>

            <!-- 价格信息 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="仅需 "
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="¥159"
                    android:textColor="@color/orange"
                    android:textSize="22sp"
                    android:textStyle="bold" />


            </LinearLayout>

            <!-- 按钮 -->
            <Button
                android:id="@+id/btn_activate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:background="@drawable/bg_button_gradient"
                android:padding="14dp"
                android:text="立即开通会员"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/btn_cancel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="?attr/selectableItemBackground"
                android:padding="8dp"
                android:text="暂不开通"
                android:textAlignment="center"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView> 