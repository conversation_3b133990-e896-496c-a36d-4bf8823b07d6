<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    
    <!-- 背景圆形 -->
    <path
        android:fillColor="@color/blue_light"
        android:pathData="M0,0h108v108h-108z" />
    
    <!-- 文件图标 -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M36,30c-1.1,0 -2,0.9 -2,2v44c0,1.1 0.9,2 2,2h36c1.1,0 2,-0.9 2,-2V42L62,30H36z" />
    
    <!-- 文件折角 -->
    <path
        android:fillColor="#E0E0E0"
        android:pathData="M62,30v12h12z" />
    
    <!-- 文件内容线条 -->
    <path
        android:strokeColor="@color/blue_primary"
        android:strokeWidth="2"
        android:pathData="M44,50h20" />
    
    <path
        android:strokeColor="#BBBBBB"
        android:strokeWidth="2"
        android:pathData="M44,58h20" />
    
    <path
        android:strokeColor="#BBBBBB"
        android:strokeWidth="2"
        android:pathData="M44,66h12" />
    
    <!-- 数据修复文字 - 简化为图形元素 -->
    <path
        android:fillColor="@color/blue_primary"
        android:pathData="M54,36m-4,0a4,4 0,1 1,8 0a4,4 0,1 1,-8 0" />
    
    <!-- 星星装饰 -->
    <path
        android:fillColor="#FFEB3B"
        android:strokeColor="#FFD600"
        android:strokeWidth="0.5"
        android:pathData="M72,36l1.5,3.5 4,0.5 -3,2.5 1,4 -3.5,-2 -3.5,2 1,-4 -3,-2.5 4,-0.5z" />
    
    <path
        android:fillColor="#FFEB3B"
        android:strokeColor="#FFD600"
        android:strokeWidth="0.5"
        android:pathData="M36,72l1,2 2.5,0.5 -2,1.5 0.5,2.5 -2,-1 -2,1 0.5,-2.5 -2,-1.5 2.5,-0.5z" />
</vector> 