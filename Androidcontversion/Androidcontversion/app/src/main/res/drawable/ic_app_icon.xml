<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">

    <!-- Background circle -->
    <path
        android:fillColor="#3F51B5"
        android:pathData="M0,0h108v108h-108z" />

    <!-- Inner gradient -->
    <path
        android:fillColor="#5367E4"
        android:pathData="M18,18h72v72h-72z" />

    <!-- Phone outline -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M44,28L64,28A3,3 0,0 1,67 31L67,77A3,3 0,0 1,64 80L44,80A3,3 0,0 1,41 77L41,31A3,3 0,0 1,44 28z" />
    
    <!-- Phone screen -->
    <path
        android:fillColor="#3F51B5"
        android:pathData="M45,32L63,32A1,1 0,0 1,64 33L64,72A1,1 0,0 1,63 73L45,73A1,1 0,0 1,44 72L44,33A1,1 0,0 1,45 32z" />
    
    <!-- Data recovery symbol -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M54,40m-4,0a4,4 0,1 1,8 0a4,4 0,1 1,-8 0" />
    
    <path
        android:strokeColor="#FFFFFF"
        android:strokeWidth="2.5"
        android:strokeLineCap="round"
        android:pathData="M54,44L54,59" />
    
    <path
        android:strokeColor="#FFFFFF"
        android:strokeWidth="2.5"
        android:strokeLineCap="round"
        android:pathData="M49,54L54,59L59,54" />
    
    <!-- Shield outline -->
    <path
        android:fillColor="#00000000"
        android:strokeColor="#FFFFFF"
        android:strokeWidth="2"
        android:pathData="M54,65m-6,0a6,6 0,1 1,12 0a6,6 0,1 1,-12 0" />
    
    <path
        android:strokeColor="#FFFFFF"
        android:strokeWidth="2"
        android:strokeLineCap="round"
        android:pathData="M51.5,65L53,66.5L56.5,63" />
</vector> 