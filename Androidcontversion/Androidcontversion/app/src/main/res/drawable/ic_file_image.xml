<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="100dp"
    android:height="100dp"
    android:viewportWidth="100"
    android:viewportHeight="100">

    <!-- 文件底层阴影 -->
    <path
        android:fillColor="#e0e0e0"
        android:pathData="M20,85 L20,15 L60,15 L80,35 L80,85 Z"
        android:translateX="3"
        android:translateY="3" />

    <!-- 文件主体 -->
    <path
        android:fillColor="#ffffff"
        android:pathData="M20,85 L20,15 L60,15 L80,35 L80,85 Z"
        android:strokeWidth="1"
        android:strokeColor="#cccccc" />

    <!-- 文件折角 -->
    <path
        android:fillColor="#f5f5f5"
        android:pathData="M60,15 L60,35 L80,35 Z"
        android:strokeWidth="1"
        android:strokeColor="#cccccc" />

    <!-- 文件内容线条 -->
    <path
        android:pathData="M30,45 L70,45"
        android:strokeWidth="2"
        android:strokeColor="#4facfe" />

    <path
        android:pathData="M30,55 L70,55"
        android:strokeWidth="2"
        android:strokeColor="#bbbbbb" />

    <path
        android:pathData="M30,65 L60,65"
        android:strokeWidth="2"
        android:strokeColor="#bbbbbb" />

    <!-- 文件图标 -->
    <path
        android:fillColor="#4facfe"
        android:pathData="M40,25 m-5,0 a5,5 0 1,0 10,0 a5,5 0 1,0 -10,0" />

    <!-- 添加星星装饰 -->
    <path
        android:fillColor="#ffeb3b"
        android:pathData="M85,15 L88,22 L95,23 L90,28 L91,35 L85,32 L79,35 L80,28 L75,23 L82,22 Z"
        android:strokeWidth="0.5"
        android:strokeColor="#ffd600" />

    <path
        android:fillColor="#ffeb3b"
        android:pathData="M35,75 L37,79 L42,80 L38,83 L39,88 L35,86 L31,88 L32,83 L28,80 L33,79 Z"
        android:strokeWidth="0.5"
        android:strokeColor="#ffd600" />
</vector> 