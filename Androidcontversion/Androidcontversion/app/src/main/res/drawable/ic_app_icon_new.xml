<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">

    <!-- Background gradient -->
    <path android:pathData="M0,0h108v108h-108z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startColor="#304FFE"
                android:endColor="#1E88E5"
                android:type="linear"
                android:angle="315"/>
        </aapt:attr>
    </path>
    
    <!-- Circle backdrop -->
    <path
        android:pathData="M54,54m-40,0a40,40 0,1 1,80 0a40,40 0,1 1,-80 0"
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.1"/>
        
    <!-- Data circle ring -->
    <path
        android:pathData="M54,54m-34,0a34,34 0,1 1,68 0a34,34 0,1 1,-68 0"
        android:strokeWidth="3"
        android:strokeColor="#FFFFFF"
        android:fillColor="#00000000"/>
    
    <!-- Data segments - represent data being recovered -->
    <path
        android:pathData="M54,20A34,34 0,0 1,88 54"
        android:strokeWidth="6"
        android:strokeColor="#4CAF50"
        android:strokeLineCap="round"/>
    
    <path
        android:pathData="M88,54A34,34 0,0 1,54 88"
        android:strokeWidth="6"
        android:strokeColor="#FFEB3B"
        android:strokeLineCap="round"/>
    
    <path
        android:pathData="M54,88A34,34 0,0 1,20 54"
        android:strokeWidth="6"
        android:strokeColor="#FF5722"
        android:strokeLineCap="round"/>
    
    <!-- Storage/Shield icon in the middle -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M54,42L41,47v7c0,6.4 4.4,12.3 13,13.9 8.6,-1.6 13,-7.5 13,-13.9v-7L54,42zM54,63c-5.7,-1.2 -8,-5.5 -8,-9v-2.3l8,3.5 8,-3.5V54c0,3.5 -2.3,7.8 -8,9z"/>
    
    <!-- Recovery arrow -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M54,59m-2.5,0a2.5,2.5 0,1 1,5 0a2.5,2.5 0,1 1,-5 0"/>
    
    <path
        android:strokeColor="#FFFFFF"
        android:strokeWidth="2"
        android:strokeLineCap="round"
        android:pathData="M54,59L54,70"/>
    
    <path
        android:strokeColor="#FFFFFF"
        android:strokeWidth="2"
        android:strokeLineCap="round"
        android:pathData="M49,65L54,70L59,65"/>
</vector> 