<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.Shoujixiufu" parent="Theme.MaterialComponents.Light.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/colorPrimaryDark</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/secondary_color</item>
        <item name="colorSecondaryVariant">@color/blue_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- 自定义属性 -->
        <item name="colorControlNormal">@color/white</item>
        <item name="android:windowBackground">@color/background</item>
        <item name="android:navigationBarColor">@color/white</item>
        <item name="android:windowLightNavigationBar" tools:targetApi="27">true</item>
        <!-- Fix for image loading -->
        <item name="android:largeHeap">true</item>
    </style>
    
    <style name="Theme.Shoujixiufu.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
    
    <style name="Theme.Shoujixiufu.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />
    
    <style name="Theme.Shoujixiufu.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />
</resources>