<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 全屏对话框样式 -->
    <style name="FullScreenDialogStyle" parent="Theme.MaterialComponents.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.5</item>
    </style>
    
    <!-- 对话框弹出动画 -->
    <style name="DialogAnimation">
        <item name="android:windowEnterAnimation">@anim/slide_in_bottom</item>
        <item name="android:windowExitAnimation">@anim/slide_out_top</item>
    </style>
    
    <!-- 按钮样式 -->
    <style name="RoundedButton">
        <item name="android:background">@drawable/bg_button_pay</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
    </style>
    
    <style name="RatingBar" parent="Theme.AppCompat">
        <item name="colorControlNormal">@color/text_secondary</item>
        <item name="colorControlActivated">@color/rating_color</item>
    </style>
</resources> 