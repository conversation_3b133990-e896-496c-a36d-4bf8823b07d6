<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手机数据帮手 - Logo设计展示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .logo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .logo-card {
            background: white;
            border-radius: 0px;
            box-shadow: none;
            padding: 20px;
            transition: transform 0.3s;
        }
        
        .logo-card:hover {
            transform: translateY(-5px);
        }
        
        .logo-title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .logo-frame {
            width: 100%;
            height: 300px;
            border: none;
            border-radius: 0px;
            overflow: hidden;
        }
        
        .logo-description {
            margin-top: 15px;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .download-btn {
            display: block;
            margin: 15px auto 0;
            padding: 8px 20px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            border-radius: 20px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .download-btn:hover {
            transform: scale(1.05);
        }
        
        footer {
            text-align: center;
            margin-top: 50px;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>手机数据帮手 - Logo设计展示</h1>
    
    <div class="logo-grid">
        <div class="logo-card">
            <div class="logo-title">文件风格Logo</div>
            <iframe class="logo-frame" src="logo_design_1.html"></iframe>
            <p class="logo-description">
                以文件图标为核心元素，突出数据修复的主题。蓝色背景搭配白色文件图标，星星元素增添活力，简洁明了地传达应用功能。
            </p>
            <button class="download-btn" onclick="alert('下载功能尚未实现')">下载SVG</button>
        </div>
        
        <div class="logo-card">
            <div class="logo-title">圆形风格Logo</div>
            <iframe class="logo-frame" src="logo_design_2.html"></iframe>
            <p class="logo-description">
                采用圆形设计，现代简约风格。内部数据线条象征数据流动与整理，蓝色渐变背景给人科技感，黄色圆点增添活力与亮点。
            </p>
            <button class="download-btn" onclick="alert('下载功能尚未实现')">下载SVG</button>
        </div>
        
        <div class="logo-card">
            <div class="logo-title">手机风格Logo</div>
            <iframe class="logo-frame" src="logo_design_3.html"></iframe>
            <p class="logo-description">
                直观展示手机元素，屏幕内的数据波浪动画生动展现数据流动与修复过程。底部的数据图标强调数据管理功能，整体设计简洁明了。
            </p>
            <button class="download-btn" onclick="alert('下载功能尚未实现')">下载SVG</button>
        </div>
        
        <div class="logo-card">
            <div class="logo-title">数据修复风格Logo</div>
            <iframe class="logo-frame" src="logo_design_4.html"></iframe>
            <p class="logo-description">
                通过二进制数据矩阵直观表现数据修复主题，黄色问号表示待修复的数据，修复工具图标象征应用功能，二进制背景增强科技感。
            </p>
            <button class="download-btn" onclick="alert('下载功能尚未实现')">下载SVG</button>
        </div>
        
        <div class="logo-card">
            <div class="logo-title">盾牌安全风格Logo</div>
            <iframe class="logo-frame" src="logo_design_5.html"></iframe>
            <p class="logo-description">
                盾牌造型象征数据安全与保护，中央手机图标直观表达应用主题，旋转数据圆环展现数据处理过程，锁图标强调隐私保护功能。
            </p>
            <button class="download-btn" onclick="alert('下载功能尚未实现')">下载SVG</button>
        </div>
        
        <div class="logo-card">
            <div class="logo-title">云存储风格Logo</div>
            <iframe class="logo-frame" src="logo_design_6.html"></iframe>
            <p class="logo-description">
                云图标象征云存储与数据备份，数据流动线条展示数据传输过程，手机图标表明应用对象，文件图标强调数据管理功能。
            </p>
            <button class="download-btn" onclick="alert('下载功能尚未实现')">下载SVG</button>
        </div>
        
        <div class="logo-card">
            <div class="logo-title">科技几何风格Logo</div>
            <iframe class="logo-frame" src="logo_design_7.html"></iframe>
            <p class="logo-description">
                六边形几何图案营造科技感，电路线条增强高科技氛围，手机图标直观表达应用主题，脉冲动画效果展示数据流动与处理过程。
            </p>
            <button class="download-btn" onclick="alert('下载功能尚未实现')">下载SVG</button>
        </div>
        
        <div class="logo-card">
            <div class="logo-title">扁平化现代风格Logo</div>
            <iframe class="logo-frame" src="logo_design_8.html"></iframe>
            <p class="logo-description">
                采用现代扁平化设计风格，对角线渐变背景增添视觉层次，手机屏幕内的数据块直观展示数据管理功能，圆形图标增添设计亮点。
            </p>
            <button class="download-btn" onclick="alert('下载功能尚未实现')">下载SVG</button>
        </div>
    </div>
    
    <footer>
        <p>© 2024 手机数据帮手 - 所有设计仅供参考</p>
    </footer>
</body>
</html> 