<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扁平化现代风格Logo</title>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f8f9fa;
        }
        
        .logo-container {
            position: relative;
            width: 200px;
            height: 200px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }
        
        .logo-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
            clip-path: polygon(0 0, 100% 0, 100% 70%, 0% 100%);
            z-index: 1;
        }
        
        .logo-content {
            position: relative;
            z-index: 2;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .phone-container {
            position: relative;
            width: 80px;
            height: 140px;
            margin-bottom: 15px;
        }
        
        .phone {
            position: absolute;
            width: 70px;
            height: 140px;
            background: #333;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .phone-screen {
            position: absolute;
            top: 10px;
            left: 5px;
            width: 60px;
            height: 120px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .data-block {
            position: absolute;
            height: 10px;
            border-radius: 2px;
        }
        
        .block1 {
            top: 15px;
            left: 10px;
            width: 40px;
            background: #4facfe;
        }
        
        .block2 {
            top: 35px;
            left: 10px;
            width: 30px;
            background: #4facfe;
        }
        
        .block3 {
            top: 55px;
            left: 10px;
            width: 40px;
            background: #4facfe;
        }
        
        .block4 {
            top: 75px;
            left: 10px;
            width: 20px;
            background: #ffeb3b;
        }
        
        .block5 {
            top: 95px;
            left: 10px;
            width: 40px;
            background: #4facfe;
        }
        
        .data-circle {
            position: absolute;
            width: 40px;
            height: 40px;
            background: #ffeb3b;
            border-radius: 50%;
            top: 50px;
            right: -20px;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
            z-index: 3;
        }
        
        .data-circle:before {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            border: 3px solid #333;
            border-radius: 50%;
        }
        
        .data-circle:after {
            content: '';
            position: absolute;
            width: 8px;
            height: 8px;
            background: #333;
            border-radius: 50%;
        }
        
        .app-title {
            font-family: Arial, sans-serif;
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-top: 10px;
            text-align: center;
        }
        
        .app-subtitle {
            font-family: Arial, sans-serif;
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        .app-name {
            position: absolute;
            bottom: -40px;
            width: 100%;
            text-align: center;
            font-family: Arial, sans-serif;
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="logo-container">
        <div class="logo-bg"></div>
        <div class="logo-content">
            <div class="phone-container">
                <div class="phone">
                    <div class="phone-screen">
                        <div class="data-block block1"></div>
                        <div class="data-block block2"></div>
                        <div class="data-block block3"></div>
                        <div class="data-block block4"></div>
                        <div class="data-block block5"></div>
                    </div>
                </div>
                <div class="data-circle"></div>
            </div>
            <div class="app-title">数据帮手</div>
            <div class="app-subtitle">安全高效 · 隐私无忧</div>
        </div>
        <div class="app-name">手机数据帮手</div>
    </div>
</body>
</html> 