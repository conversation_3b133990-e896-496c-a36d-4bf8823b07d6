<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手机风格Logo</title>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f8f9fa;
        }
        
        .logo-container {
            position: relative;
            width: 200px;
            height: 200px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .phone {
            position: relative;
            width: 80px;
            height: 140px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        
        .phone-screen {
            position: absolute;
            top: 10px;
            left: 5px;
            width: 70px;
            height: 120px;
            background: #333;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .data-wave {
            position: absolute;
            height: 40px;
            width: 200%;
            background: linear-gradient(90deg, #4facfe, #00f2fe, #4facfe);
            animation: wave 3s linear infinite;
        }
        
        .wave1 {
            top: 20px;
            opacity: 0.7;
        }
        
        .wave2 {
            top: 40px;
            opacity: 0.5;
            animation-delay: 0.5s;
        }
        
        .wave3 {
            top: 60px;
            opacity: 0.3;
            animation-delay: 1s;
        }
        
        @keyframes wave {
            0% {
                transform: translateX(-50%);
            }
            100% {
                transform: translateX(0%);
            }
        }
        
        .data-icon {
            position: absolute;
            bottom: 20px;
            left: 25px;
            width: 30px;
            height: 30px;
            background: #ffeb3b;
            border-radius: 50%;
        }
        
        .data-icon:before {
            content: '';
            position: absolute;
            top: 10px;
            left: 7px;
            width: 16px;
            height: 10px;
            border-top: 3px solid #333;
            border-left: 3px solid #333;
            border-right: 3px solid #333;
            border-radius: 3px 3px 0 0;
        }
        
        .data-icon:after {
            content: '';
            position: absolute;
            bottom: 7px;
            left: 13px;
            width: 4px;
            height: 10px;
            background: #333;
        }
        
        .app-name {
            position: absolute;
            bottom: -40px;
            width: 100%;
            text-align: center;
            font-family: Arial, sans-serif;
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="logo-container">
        <div class="phone">
            <div class="phone-screen">
                <div class="data-wave wave1"></div>
                <div class="data-wave wave2"></div>
                <div class="data-wave wave3"></div>
                <div class="data-icon"></div>
            </div>
        </div>
        <div class="app-name">手机数据帮手</div>
    </div>
</body>
</html> 